// src/data/states.ts
export interface VehicleType {
  id: string;
  name: string;
  mmv: number;
}

export const vehicleTypes: VehicleType[] = [
  { id: "motocicleta_1_5", name: "Motocicleta - Cuando el modelo sea de 1 a 5 años", mmv: 7 },
  { id: "motocicleta_mayor_5", name: "Motocicleta - Cuando el modelo sea mayor de 5 años", mmv: 5 },
  { id: "autos_1_5", name: "Automóviles destinados a uso particular - Cuando el modelo sea de 1 a 5 años", mmv: 8 },
  { id: "autos_6_10", name: "Automóviles destinados a uso particular - Cuando el modelo sea de 6 a 10 años", mmv: 6 },
  { id: "autos_mayor_10", name: "Automóviles destinados a uso particular - Cuando el modelo sea mayor de 10 años", mmv: 5 },
  { id: "camionetas_1_5", name: "Camionetas destinadas a uso particular - Cuando el modelo sea de 1 a 5 años", mmv: 10 },
  { id: "camionetas_6_10", name: "Camionetas destinadas a uso particular - Cuando el modelo sea de 6 a 10 años", mmv: 9 },
  { id: "camionetas_mayor_10", name: "Camionetas destinadas a uso particular - Cuando el modelo sea mayor de 10 años", mmv: 8 },
  { id: "transporte_pasajeros_hasta_10", name: "Transporte de pasajeros - Hasta 10 puestos", mmv: 10 },
  { id: "transporte_pasajeros_hasta_20", name: "Transporte de pasajeros - Hasta 20 puestos", mmv: 15 },
  { id: "transporte_pasajeros_mas_20", name: "Transporte de pasajeros - Más de 20 puestos", mmv: 20 },
  { id: "transporte_escolar_hasta_10", name: "Transporte escolar - Hasta 10 puestos", mmv: 5 },
  { id: "transporte_escolar_hasta_20", name: "Transporte escolar - Hasta 20 puestos", mmv: 10 },
  { id: "transporte_escolar_mas_20", name: "Transporte escolar - Más de 20 puestos", mmv: 15 },
  { id: "transporte_carga_liviana_hasta_3300", name: "Transporte de carga liviana - Peso del vehículo inferior a 3.300 kg", mmv: 20 },
  { id: "transporte_carga_liviana_mas_3301", name: "Transporte de carga liviana - Peso del vehículo superior a 3.301 kg", mmv: 60 },
  { id: "ambulancia", name: "Ambulancia", mmv: 8 },
  { id: "carroza_funebre", name: "Carroza fúnebre", mmv: 8 },
  { id: "transporte_valores", name: "Transporte de valores", mmv: 10 },
  { id: "otros", name: "Otros vehículos", mmv: 12 }
];