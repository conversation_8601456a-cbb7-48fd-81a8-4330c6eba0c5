// src/pages/publicidad/PublicidadNew.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  PlusIcon,
  TrashIcon,
  UserIcon,
  MegaphoneIcon
} from '@heroicons/react/24/outline';
import { useToast } from '../../components/ToastProvider';
import AdvertisingConfirmationModal from '../../components/AdvertisingConfirmationModal';
import type {
  AdvertisingFormData,
  AdvertisingEntry,
  AdvertisingRegistrationData
} from '../../types/advertising';
import {
  ADVERTISING_TYPES,
  TAX_RATES
} from '../../types/advertising';
import { MONTHS } from '../../types/misc';

const Publicidad: React.FC = () => {
  const { showToast } = useToast();
  
  const [formData, setFormData] = useState<AdvertisingFormData>({
    contributor: {
      rif: 'J-********-9',
      nameOrBusinessName: 'Empresa Ejemplo C.A.',
      accountNumber: '**********',
      phone: '+58 ************',
      email: '<EMAIL>',
      address: 'Av. Principal, Edificio Centro, Piso 5',
      state: 'Distrito Capital',
      municipality: 'Libertador'
    },
    advertisingType: {
      type: '',
      description: '',
      exactAddress: '',
      dimensions: '',
      area: 0,
      numberOfElements: 0,
      duration: {
        start: '',
        end: ''
      },
      numberOfDays: 0,
      numberOfPromoters: 0,
      fraction: 0
    },
    declaration: {
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      exchangeType: 0,
      taxAmount: 0
    }
  });

  const [advertisingEntries, setAdvertisingEntries] = useState<AdvertisingEntry[]>([]);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<Record<string, boolean>>({});

  // Set page title
  useEffect(() => {
    document.title = 'Taxia | Publicidad y Propaganda';
  }, []);

  // Calculate number of days when duration changes
  useEffect(() => {
    if (formData.advertisingType.duration.start && formData.advertisingType.duration.end) {
      const startDate = new Date(formData.advertisingType.duration.start);
      const endDate = new Date(formData.advertisingType.duration.end);
      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      
      setFormData(prev => ({
        ...prev,
        advertisingType: {
          ...prev.advertisingType,
          numberOfDays: diffDays
        }
      }));
    }
  }, [formData.advertisingType.duration.start, formData.advertisingType.duration.end]);

  // Calculate tax amount when relevant fields change
  useEffect(() => {
    const { type, area, numberOfElements, numberOfDays, fraction } = formData.advertisingType;
    
    if (type && area > 0 && numberOfElements > 0 && numberOfDays > 0 && fraction > 0) {
      const baseRate = TAX_RATES[type as keyof typeof TAX_RATES] || 0.03;
      const fractionValue = fraction; // fraction is now a number
      const baseAmount = area * numberOfElements * numberOfDays * fractionValue;
      const taxAmount = baseAmount * baseRate;
      
      setFormData(prev => ({
        ...prev,
        declaration: {
          ...prev.declaration,
          taxAmount: taxAmount
        }
      }));
    }
  }, [
    formData.advertisingType.type,
    formData.advertisingType.area,
    formData.advertisingType.numberOfElements,
    formData.advertisingType.numberOfDays,
    formData.advertisingType.fraction
  ]);

  const handleInputChange = (section: keyof AdvertisingFormData, field: string, value: any) => {
    // Clear error for this field when user starts typing
    const fieldKey = `${section}.${field}`;
    if (fieldErrors[fieldKey]) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldKey];
        return newErrors;
      });
    }

    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleNestedInputChange = (section: keyof AdvertisingFormData, nestedField: string, field: string, value: any) => {
    // Clear error for this field when user starts typing
    const fieldKey = `${section}.${nestedField}.${field}`;
    if (fieldErrors[fieldKey]) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldKey];
        return newErrors;
      });
    }

    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [nestedField]: {
          ...(prev[section] as any)[nestedField],
          [field]: value
        }
      }
    }));
  };

  const addAdvertisingEntry = () => {
    const { type, description, exactAddress, area, numberOfElements, numberOfDays, fraction } = formData.advertisingType;
    const { exchangeType, taxAmount } = formData.declaration;

    // Reset previous errors
    setFieldErrors({});

    // Validation with visual feedback
    const errors: Record<string, boolean> = {};

    if (!type) errors['advertisingType.type'] = true;
    if (!description) errors['advertisingType.description'] = true;
    if (!exactAddress.trim()) errors['advertisingType.exactAddress'] = true;
    if (!formData.advertisingType.dimensions) errors['advertisingType.dimensions'] = true;
    if (area <= 0) errors['advertisingType.area'] = true;
    if (numberOfElements <= 0) errors['advertisingType.numberOfElements'] = true;
    if (!formData.advertisingType.duration.start) errors['advertisingType.duration.start'] = true;
    if (!formData.advertisingType.duration.end) errors['advertisingType.duration.end'] = true;
    if (numberOfDays <= 0) errors['advertisingType.numberOfDays'] = true;
    if (formData.advertisingType.numberOfPromoters <= 0) errors['advertisingType.numberOfPromoters'] = true;
    if (fraction <= 0) errors['advertisingType.fraction'] = true;
    if (exchangeType <= 0) errors['declaration.exchangeType'] = true;

    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      showToast('Error', 'Por favor complete todos los campos del tipo de publicidad', 'error');
      return;
    }

    const newEntry: AdvertisingEntry = {
      id: Date.now().toString(),
      type: ADVERTISING_TYPES.find(t => t.value === type)?.label || type,
      description,
      exactAddress,
      area,
      numberOfElements,
      numberOfDays,
      fraction,
      exchangeType,
      month: formData.declaration.month,
      year: formData.declaration.year,
      taxAmount
    };

    setAdvertisingEntries(prev => [...prev, newEntry]);

    // Reset advertising type form
    setFormData(prev => ({
      ...prev,
      advertisingType: {
        type: '',
        description: '',
        exactAddress: '',
        dimensions: '',
        area: 0,
        numberOfElements: 0,
        duration: {
          start: '',
          end: ''
        },
        numberOfDays: 0,
        numberOfPromoters: 0,
        fraction: 0
      },
      declaration: {
        ...prev.declaration,
        exchangeType: 0,
        taxAmount: 0
      }
    }));

    showToast('Éxito', 'Tipo de publicidad agregado correctamente', 'success');
  };

  const removeAdvertisingEntry = (id: string) => {
    setAdvertisingEntries(prev => prev.filter(entry => entry.id !== id));
    showToast('Éxito', 'Tipo de publicidad eliminado', 'success');
  };

  const calculateTotal = (): number => {
    return advertisingEntries.reduce((total, entry) => total + entry.taxAmount, 0);
  };

  const handleSubmit = () => {
    // Reset previous errors
    setFieldErrors({});

    // Validation with visual feedback
    const errors: Record<string, boolean> = {};

    if (!formData.declaration.month || formData.declaration.month < 1 || formData.declaration.month > 12) {
      errors['declaration.month'] = true;
    }

    if (!formData.declaration.year || formData.declaration.year < 2020) {
      errors['declaration.year'] = true;
    }

    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      showToast('Error', 'Por favor complete todos los campos obligatorios', 'error');
      return;
    }

    if (advertisingEntries.length === 0) {
      showToast('Error', 'Debe agregar al menos un tipo de publicidad', 'error');
      return;
    }

    setIsConfirmationModalOpen(true);
  };

  const handleConfirmSubmission = async () => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Datos de publicidad enviados:', getConfirmationData());
      
      showToast('Éxito', 'Declaración de publicidad registrada exitosamente', 'success');
      
      // Reset form
      setFormData(prev => ({
        ...prev,
        advertisingType: {
          type: '',
          description: '',
          exactAddress: '',
          dimensions: '',
          area: 0,
          numberOfElements: 0,
          duration: {
            start: '',
            end: ''
          },
          numberOfDays: 0,
          numberOfPromoters: 0,
          fraction: 0
        },
        declaration: {
          month: new Date().getMonth() + 1,
          year: new Date().getFullYear(),
          exchangeType: 0,
          taxAmount: 0
        }
      }));
      
      setAdvertisingEntries([]);
      setIsConfirmationModalOpen(false);
      
    } catch (error) {
      console.error('Error submitting advertising registration:', error);
      showToast('Error', 'Error al registrar la declaración. Por favor intente nuevamente.', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getConfirmationData = (): AdvertisingRegistrationData => ({
    rif: formData.contributor.rif,
    nameOrBusinessName: formData.contributor.nameOrBusinessName,
    accountNumber: formData.contributor.accountNumber,
    phone: formData.contributor.phone,
    email: formData.contributor.email,
    address: formData.contributor.address,
    state: formData.contributor.state,
    municipality: formData.contributor.municipality,
    month: formData.declaration.month,
    year: formData.declaration.year,
    advertisingEntries,
    totalAmount: calculateTotal()
  });

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('es-VE', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  return (
    <Container>
      <Header>
        <Title>Publicidad y Propaganda</Title>
        <Subtitle>Registro de declaración de impuestos sobre publicidad y propaganda comercial</Subtitle>
      </Header>

      <Content>
        {/* Datos del Contribuyente */}
        <FormSection>
          <SectionTitle>
            <UserIcon />
            Datos del Contribuyente
          </SectionTitle>
          <FormGrid>
            <FormGroup>
              <Label>RIF</Label>
              <Input
                type="text"
                value={formData.contributor.rif}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup>
              <Label>Apellidos y Nombres / Razón Social</Label>
              <Input
                type="text"
                value={formData.contributor.nameOrBusinessName}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup>
              <Label>N° de Cuenta</Label>
              <Input
                type="text"
                value={formData.contributor.accountNumber}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup>
              <Label>Teléfonos</Label>
              <Input
                type="text"
                value={formData.contributor.phone}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup>
              <Label>Correos</Label>
              <Input
                type="email"
                value={formData.contributor.email}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup className="full-width">
              <Label>Dirección</Label>
              <Input
                type="text"
                value={formData.contributor.address}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup>
              <Label>Estado</Label>
              <Select
                value={formData.contributor.state}
                disabled
              >
                <option value="">{formData.contributor.state}</option>
              </Select>
            </FormGroup>
            <FormGroup>
              <Label>Municipio</Label>
              <Select
                value={formData.contributor.municipality}
                disabled
              >
                <option value="">{formData.contributor.municipality}</option>
              </Select>
            </FormGroup>
          </FormGrid>
        </FormSection>

        {/* Tipo de Publicidad */}
        <FormSection>
          <SectionTitle>
            <MegaphoneIcon />
            Tipo de Publicidad
          </SectionTitle>
          <FormGrid>
            <FormGroup>
              <Label>Tipo de Publicidad *</Label>
              <Select
                value={formData.advertisingType.type}
                onChange={(e) => handleInputChange('advertisingType', 'type', e.target.value)}
                $hasError={fieldErrors['advertisingType.type']}
              >
                <option value="">Seleccione tipo</option>
                {ADVERTISING_TYPES.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </Select>
            </FormGroup>
            <FormGroup>
              <Label>Descripción del medio publicitario *</Label>
              <Input
                type="text"
                value={formData.advertisingType.description}
                onChange={(e) => handleInputChange('advertisingType', 'description', e.target.value)}
                placeholder="Descripción detallada"
                $hasError={fieldErrors['advertisingType.description']}
              />
            </FormGroup>
            <FormGroup className="full-width">
              <Label>Dirección exacta del establecimiento o lugar de publicidad *</Label>
              <Input
                type="text"
                value={formData.advertisingType.exactAddress}
                onChange={(e) => handleInputChange('advertisingType', 'exactAddress', e.target.value)}
                placeholder="Ingrese la dirección exacta"
                $hasError={fieldErrors['advertisingType.exactAddress']}
              />
            </FormGroup>
            <FormGroup>
              <Label>Dimensiones y características físicas *</Label>
              <Input
                type="text"
                value={formData.advertisingType.dimensions}
                onChange={(e) => handleInputChange('advertisingType', 'dimensions', e.target.value)}
                placeholder="Ej: 3m x 2m"
                $hasError={fieldErrors['advertisingType.dimensions']}
              />
            </FormGroup>
            <FormGroup>
              <Label>Área del elemento (Mts2) *</Label>
              <Input
                type="number"
                value={formData.advertisingType.area}
                onChange={(e) => handleInputChange('advertisingType', 'area', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                step="0.01"
                min="0"
                $hasError={fieldErrors['advertisingType.area']}
              />
            </FormGroup>
            <FormGroup>
              <Label>Número de elementos *</Label>
              <Input
                type="number"
                value={formData.advertisingType.numberOfElements}
                onChange={(e) => handleInputChange('advertisingType', 'numberOfElements', parseInt(e.target.value) || 0)}
                placeholder="0"
                min="0"
                $hasError={fieldErrors['advertisingType.numberOfElements']}
              />
            </FormGroup>
            <FormGroup>
              <Label>Duración de la publicidad - Inicio *</Label>
              <Input
                type="date"
                value={formData.advertisingType.duration.start}
                onChange={(e) => handleNestedInputChange('advertisingType', 'duration', 'start', e.target.value)}
                $hasError={fieldErrors['advertisingType.duration.start']}
              />
            </FormGroup>
            <FormGroup>
              <Label>Duración de la publicidad - Fin *</Label>
              <Input
                type="date"
                value={formData.advertisingType.duration.end}
                onChange={(e) => handleNestedInputChange('advertisingType', 'duration', 'end', e.target.value)}
                $hasError={fieldErrors['advertisingType.duration.end']}
              />
            </FormGroup>
            <FormGroup>
              <Label>Número de días *</Label>
              <Input
                type="number"
                value={formData.advertisingType.numberOfDays}
                onChange={(e) => handleInputChange('advertisingType', 'numberOfDays', parseInt(e.target.value) || 0)}
                placeholder="0"
                min="0"
                $hasError={fieldErrors['advertisingType.numberOfDays']}
              />
            </FormGroup>
            <FormGroup>
              <Label>Número de promotores *</Label>
              <Input
                type="number"
                value={formData.advertisingType.numberOfPromoters}
                onChange={(e) => handleInputChange('advertisingType', 'numberOfPromoters', parseInt(e.target.value) || 0)}
                placeholder="0"
                min="0"
                $hasError={fieldErrors['advertisingType.numberOfPromoters']}
              />
            </FormGroup>
            <FormGroup>
              <Label>Fracción *</Label>
              <Input
                type="number"
                value={formData.advertisingType.fraction}
                onChange={(e) => handleInputChange('advertisingType', 'fraction', e.target.value)}
                placeholder="0.00"
                step="0.01"
                min="0"
                max="1"
                $hasError={fieldErrors['advertisingType.fraction']}
              />
            </FormGroup>
            <FormGroup>
              <Label>Mes de la declaración *</Label>
              <Select
                value={formData.declaration.month}
                onChange={(e) => handleInputChange('declaration', 'month', parseInt(e.target.value) || 0)}
                $hasError={fieldErrors['declaration.month']}
              >
                <option value="">Seleccione mes</option>
                {MONTHS.map(month => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </Select>
            </FormGroup>
            <FormGroup>
              <Label>Año de la Declaración *</Label>
              <Input
                type="number"
                value={formData.declaration.year || new Date().getFullYear()}
                onChange={(e) => handleInputChange('declaration', 'year', parseInt(e.target.value) || new Date().getFullYear())}
                placeholder={new Date().getFullYear().toString()}
                min="2020"
                max="2030"
                $hasError={fieldErrors['declaration.year']}
              />
            </FormGroup>
            <FormGroup>
              <Label>Tipo de cambio (MMV) *</Label>
              <Input
                type="number"
                value={formData.declaration.exchangeType}
                onChange={(e) => handleInputChange('declaration', 'exchangeType', e.target.value)}
                placeholder="0.00"
                step="0.01"
                min="0"
                $hasError={fieldErrors['declaration.exchangeType']}
              />
            </FormGroup>
            <FormGroup>
              <Label>Monto Impuesto (Bs.)</Label>
              <Input
                type="text"
                value={formatCurrency(formData.declaration.taxAmount)}
                readOnly
                disabled
              />
            </FormGroup>
          </FormGrid>

          <AddButton onClick={addAdvertisingEntry}>
            <PlusIcon />
            Agregar Tipo de Publicidad
          </AddButton>
        </FormSection>

        {/* Tabla de Publicidad Registrada */}
        {advertisingEntries.length > 0 && (
          <TableSection>
            <SectionTitle>Publicidad Registrada</SectionTitle>
            <TableContainer>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHeaderCell>Tipo de Publicidad</TableHeaderCell>
                    <TableHeaderCell>Descripción del medio publicitario</TableHeaderCell>
                    <TableHeaderCell>Dirección exacta</TableHeaderCell>
                    <TableHeaderCell>Mts2</TableHeaderCell>
                    <TableHeaderCell>N° de elementos</TableHeaderCell>
                    <TableHeaderCell>N° de días</TableHeaderCell>
                    <TableHeaderCell>Fracción</TableHeaderCell>
                    <TableHeaderCell>Tipo de cambio (MMV)</TableHeaderCell>
                    <TableHeaderCell>Mes</TableHeaderCell>
                    <TableHeaderCell>Año</TableHeaderCell>
                    <TableHeaderCell>Monto Impuesto (Bs.)</TableHeaderCell>
                    <TableHeaderCell>Acciones</TableHeaderCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {advertisingEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>{entry.type}</TableCell>
                      <TableCell>{entry.description}</TableCell>
                      <TableCell>{entry.exactAddress}</TableCell>
                      <TableCell>{entry.area}</TableCell>
                      <TableCell>{entry.numberOfElements}</TableCell>
                      <TableCell>{entry.numberOfDays}</TableCell>
                      <TableCell>{entry.fraction}</TableCell>
                      <TableCell>{entry.exchangeType}</TableCell>
                      <TableCell>{entry.month}</TableCell>
                      <TableCell>{entry.year}</TableCell>
                      <TableCell>{formatCurrency(entry.taxAmount)}</TableCell>
                      <TableCell>
                        <DeleteButton onClick={() => removeAdvertisingEntry(entry.id)}>
                          <TrashIcon />
                        </DeleteButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TotalSection>
              <TotalLabel>Total a pagar:</TotalLabel>
              <TotalAmount>{formatCurrency(calculateTotal())}</TotalAmount>
            </TotalSection>
          </TableSection>
        )}

        {/* Submit Button */}
        <SubmitSection>
          <SubmitButton onClick={handleSubmit}>
            Enviar Registro
          </SubmitButton>
        </SubmitSection>
      </Content>

      {/* Confirmation Modal */}
      <AdvertisingConfirmationModal
        isOpen={isConfirmationModalOpen}
        onClose={() => setIsConfirmationModalOpen(false)}
        onConfirm={handleConfirmSubmission}
        data={getConfirmationData()}
        isLoading={isSubmitting}
      />
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  min-height: 100vh;
  background-color: ${props => props.theme.colors.background};
  transition: background-color 0.3s ease;
`;

const Header = styled.div`
  background-color: ${props => props.theme.colors.white};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  padding: ${props => props.theme.space[8]} ${props => props.theme.space[6]};
  transition: all 0.3s ease;
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.space[2]};
  transition: color 0.3s ease;
`;

const Subtitle = styled.p`
  font-size: ${props => props.theme.fontSizes.lg};
  color: ${props => props.theme.colors.textLight};
  margin: 0;
  transition: color 0.3s ease;
`;

const Content = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${props => props.theme.space[8]} ${props => props.theme.space[6]};
`;

const FormSection = styled.div`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  padding: ${props => props.theme.space[6]};
  margin-bottom: ${props => props.theme.space[6]};
  box-shadow: ${props => props.theme.shadows.sm};
  border: 1px solid ${props => props.theme.colors.borderLight};
  transition: all 0.3s ease;
`;

const TableSection = styled(FormSection)``;

const SectionTitle = styled.h2`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.space[6]};
  transition: color 0.3s ease;

  svg {
    width: 20px;
    height: 20px;
    color: ${props => props.theme.colors.primary};
  }
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.space[4]};

  @media (min-width: 640px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[2]};

  &.full-width {
    grid-column: 1 / -1;
  }
`;

const Label = styled.label`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const Input = styled.input<{ $hasError?: boolean }>`
  padding: ${props => props.theme.space[3]};
  border: 1px solid ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  background-color: ${props => props.theme.colors.white};
  color: ${props => props.theme.colors.text};
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.primary}20;
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.textLight};
    cursor: not-allowed;
  }

  &::placeholder {
    color: ${props => props.theme.colors.textLight};
  }
`;

const Select = styled.select<{ $hasError?: boolean }>`
  padding: ${props => props.theme.space[3]};
  border: 1px solid ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  background-color: ${props => props.theme.colors.white};
  color: ${props => props.theme.colors.text};
  cursor: pointer;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.primary}20;
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.textLight};
    cursor: not-allowed;
  }
`;

const AddButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  margin-top: ${props => props.theme.space[4]};
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.primaryDark};
  }

  svg {
    width: 20px;
    height: 20px;
  }
`;

const TableContainer = styled.div`
  overflow-x: auto;
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  transition: border-color 0.3s ease;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
`;

const TableHeader = styled.thead`
  background-color: ${props => props.theme.colors.backgroundMuted};
  transition: background-color 0.3s ease;
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const TableHeaderCell = styled.th`
  padding: ${props => props.theme.space[4]};
  text-align: left;
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  font-size: ${props => props.theme.fontSizes.sm};
  white-space: nowrap;
  transition: color 0.3s ease;
`;

const TableCell = styled.td`
  padding: ${props => props.theme.space[4]};
  color: ${props => props.theme.colors.text};
  font-size: ${props => props.theme.fontSizes.sm};
  transition: color 0.3s ease;
`;

const DeleteButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: ${props => props.theme.colors.danger};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.dangerDark};
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;

const TotalSection = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: ${props => props.theme.space[4]};
  padding: ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  margin-top: ${props => props.theme.space[4]};
  transition: background-color 0.3s ease;
`;

const TotalLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const TotalAmount = styled.span`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.primary};
  transition: color 0.3s ease;
`;

const SubmitSection = styled.div`
  display: flex;
  justify-content: center;
  margin-top: ${props => props.theme.space[8]};
`;

const SubmitButton = styled.button`
  padding: ${props => props.theme.space[4]} ${props => props.theme.space[8]};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.primaryDark};
  }
`;

export default Publicidad;
