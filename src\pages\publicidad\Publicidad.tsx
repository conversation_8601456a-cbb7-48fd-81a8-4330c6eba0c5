// src/pages/publicidad/PublicidadNew.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  PlusIcon, 
  TrashIcon, 
  UserIcon, 
  DocumentTextIcon, 
  MegaphoneIcon 
} from '@heroicons/react/24/outline';
import { useToast } from '../../components/ToastProvider';
import { states } from '../../data/states';
import { municipalities } from '../../data/municipalities';
import AdvertisingConfirmationModal from '../../components/AdvertisingConfirmationModal';
import type { 
  AdvertisingFormData, 
  AdvertisingEntry, 
  AdvertisingRegistrationData 
} from '../../types/advertising';
import { 
  ADVERTISING_TYPES, 
  EXCHANGE_TYPES, 
  FRACTIONS, 
  PERIODS, 
  TAX_RATES 
} from '../../types/advertising';

const Publicidad: React.FC = () => {
  const { showToast } = useToast();
  
  const [formData, setFormData] = useState<AdvertisingFormData>({
    contributor: {
      rif: 'J-********-9',
      nameOrBusinessName: 'Empresa Ejemplo C.A.',
      accountNumber: '**********',
      phone: '+58 ************',
      email: '<EMAIL>',
      address: 'Av. Principal, Edificio Centro, Piso 5',
      state: 'Distrito Capital',
      municipality: 'Libertador'
    },
    registrationDetails: {
      exactAddress: ''
    },
    advertisingType: {
      type: '',
      description: '',
      dimensions: '',
      area: 0,
      numberOfElements: 0,
      duration: {
        start: '',
        end: ''
      },
      numberOfDays: 0,
      numberOfPromoters: 0,
      fraction: ''
    },
    declaration: {
      period: '',
      exchangeType: '',
      taxAmount: 0
    }
  });

  const [advertisingEntries, setAdvertisingEntries] = useState<AdvertisingEntry[]>([]);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<Record<string, boolean>>({});

  // Set page title
  useEffect(() => {
    document.title = 'Taxlite | Publicidad y Propaganda';
  }, []);

  // Calculate number of days when duration changes
  useEffect(() => {
    if (formData.advertisingType.duration.start && formData.advertisingType.duration.end) {
      const startDate = new Date(formData.advertisingType.duration.start);
      const endDate = new Date(formData.advertisingType.duration.end);
      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      
      setFormData(prev => ({
        ...prev,
        advertisingType: {
          ...prev.advertisingType,
          numberOfDays: diffDays
        }
      }));
    }
  }, [formData.advertisingType.duration.start, formData.advertisingType.duration.end]);

  // Calculate tax amount when relevant fields change
  useEffect(() => {
    const { type, area, numberOfElements, numberOfDays, fraction } = formData.advertisingType;
    
    if (type && area > 0 && numberOfElements > 0 && numberOfDays > 0 && fraction) {
      const baseRate = TAX_RATES[type as keyof typeof TAX_RATES] || 0.03;
      const fractionValue = eval(fraction); // Convert fraction string to decimal
      const baseAmount = area * numberOfElements * numberOfDays * fractionValue;
      const taxAmount = baseAmount * baseRate;
      
      setFormData(prev => ({
        ...prev,
        declaration: {
          ...prev.declaration,
          taxAmount: taxAmount
        }
      }));
    }
  }, [
    formData.advertisingType.type,
    formData.advertisingType.area,
    formData.advertisingType.numberOfElements,
    formData.advertisingType.numberOfDays,
    formData.advertisingType.fraction
  ]);

  const handleInputChange = (section: keyof AdvertisingFormData, field: string, value: any) => {
    // Clear error for this field when user starts typing
    const fieldKey = `${section}.${field}`;
    if (fieldErrors[fieldKey]) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldKey];
        return newErrors;
      });
    }

    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleNestedInputChange = (section: keyof AdvertisingFormData, nestedField: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [nestedField]: {
          ...(prev[section] as any)[nestedField],
          [field]: value
        }
      }
    }));
  };

  const addAdvertisingEntry = () => {
    const { type, description, area, numberOfElements, numberOfDays, fraction } = formData.advertisingType;
    const { exchangeType, taxAmount } = formData.declaration;

    // Reset previous errors
    setFieldErrors({});

    // Validation with visual feedback
    const errors: Record<string, boolean> = {};
    
    if (!type) errors['advertisingType.type'] = true;
    if (!description) errors['advertisingType.description'] = true;
    if (area <= 0) errors['advertisingType.area'] = true;
    if (numberOfElements <= 0) errors['advertisingType.numberOfElements'] = true;
    if (numberOfDays <= 0) errors['advertisingType.numberOfDays'] = true;
    if (!fraction) errors['advertisingType.fraction'] = true;
    if (!exchangeType) errors['declaration.exchangeType'] = true;

    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      showToast('Error', 'Por favor complete todos los campos del tipo de publicidad', 'error');
      return;
    }

    const newEntry: AdvertisingEntry = {
      id: Date.now().toString(),
      type: ADVERTISING_TYPES.find(t => t.value === type)?.label || type,
      description,
      area,
      numberOfElements,
      numberOfDays,
      fraction,
      exchangeType: EXCHANGE_TYPES.find(e => e.value === exchangeType)?.label || exchangeType,
      taxAmount
    };

    setAdvertisingEntries(prev => [...prev, newEntry]);
    
    // Reset advertising type form
    setFormData(prev => ({
      ...prev,
      advertisingType: {
        type: '',
        description: '',
        dimensions: '',
        area: 0,
        numberOfElements: 0,
        duration: {
          start: '',
          end: ''
        },
        numberOfDays: 0,
        numberOfPromoters: 0,
        fraction: ''
      },
      declaration: {
        ...prev.declaration,
        exchangeType: '',
        taxAmount: 0
      }
    }));

    showToast('Éxito', 'Tipo de publicidad agregado correctamente', 'success');
  };

  const removeAdvertisingEntry = (id: string) => {
    setAdvertisingEntries(prev => prev.filter(entry => entry.id !== id));
    showToast('Éxito', 'Tipo de publicidad eliminado', 'success');
  };

  const calculateTotal = (): number => {
    return advertisingEntries.reduce((total, entry) => total + entry.taxAmount, 0);
  };

  const handleSubmit = () => {
    // Reset previous errors
    setFieldErrors({});

    // Validation with visual feedback
    const errors: Record<string, boolean> = {};
    
    if (!formData.registrationDetails.exactAddress.trim()) {
      errors['registrationDetails.exactAddress'] = true;
    }

    if (!formData.declaration.period) {
      errors['declaration.period'] = true;
    }

    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      showToast('Error', 'Por favor complete todos los campos obligatorios', 'error');
      return;
    }

    if (advertisingEntries.length === 0) {
      showToast('Error', 'Debe agregar al menos un tipo de publicidad', 'error');
      return;
    }

    setIsConfirmationModalOpen(true);
  };

  const handleConfirmSubmission = async () => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      showToast('Éxito', 'Declaración de publicidad registrada exitosamente', 'success');
      
      // Reset form
      setFormData(prev => ({
        ...prev,
        registrationDetails: {
          exactAddress: ''
        },
        advertisingType: {
          type: '',
          description: '',
          dimensions: '',
          area: 0,
          numberOfElements: 0,
          duration: {
            start: '',
            end: ''
          },
          numberOfDays: 0,
          numberOfPromoters: 0,
          fraction: ''
        },
        declaration: {
          period: '',
          exchangeType: '',
          taxAmount: 0
        }
      }));
      
      setAdvertisingEntries([]);
      setIsConfirmationModalOpen(false);
      
    } catch (error) {
      console.error('Error submitting advertising registration:', error);
      showToast('Error', 'Error al registrar la declaración. Por favor intente nuevamente.', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getConfirmationData = (): AdvertisingRegistrationData => ({
    rif: formData.contributor.rif,
    nameOrBusinessName: formData.contributor.nameOrBusinessName,
    accountNumber: formData.contributor.accountNumber,
    phone: formData.contributor.phone,
    email: formData.contributor.email,
    address: formData.contributor.address,
    state: formData.contributor.state,
    municipality: formData.contributor.municipality,
    exactAddress: formData.registrationDetails.exactAddress,
    period: formData.declaration.period,
    advertisingEntries,
    totalAmount: calculateTotal()
  });

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('es-VE', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  return (
    <Container>
      <Header>
        <Title>Publicidad y Propaganda</Title>
        <Subtitle>Registro de declaración de impuestos sobre publicidad y propaganda comercial</Subtitle>
      </Header>

      <Content>
        {/* Datos del Contribuyente */}
        <FormSection>
          <SectionTitle>
            <UserIcon />
            Datos del Contribuyente
          </SectionTitle>
          <FormGrid>
            <FormGroup>
              <Label>1. RIF</Label>
              <Input
                type="text"
                value={formData.contributor.rif}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup>
              <Label>2. Apellidos y Nombres / Razón Social</Label>
              <Input
                type="text"
                value={formData.contributor.nameOrBusinessName}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup>
              <Label>3. N° de Cuenta</Label>
              <Input
                type="text"
                value={formData.contributor.accountNumber}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup>
              <Label>4. Teléfonos</Label>
              <Input
                type="text"
                value={formData.contributor.phone}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup>
              <Label>5. Correos</Label>
              <Input
                type="email"
                value={formData.contributor.email}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup className="full-width">
              <Label>6. Dirección</Label>
              <Input
                type="text"
                value={formData.contributor.address}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup>
              <Label>7. Estado</Label>
              <Select
                value={formData.contributor.state}
                disabled
              >
                <option value="">{formData.contributor.state}</option>
              </Select>
            </FormGroup>
            <FormGroup>
              <Label>8. Municipio</Label>
              <Select
                value={formData.contributor.municipality}
                disabled
              >
                <option value="">{formData.contributor.municipality}</option>
              </Select>
            </FormGroup>
          </FormGrid>
        </FormSection>

        {/* Detalles del Registro */}
        <FormSection>
          <SectionTitle>
            <DocumentTextIcon />
            Detalles del Registro
          </SectionTitle>
          <FormGrid>
            <FormGroup className="full-width">
              <Label>9. Dirección exacta del establecimiento o lugar de publicidad *</Label>
              <Input
                type="text"
                value={formData.registrationDetails.exactAddress}
                onChange={(e) => handleInputChange('registrationDetails', 'exactAddress', e.target.value)}
                placeholder="Ingrese la dirección exacta"
                $hasError={fieldErrors['registrationDetails.exactAddress']}
              />
            </FormGroup>
          </FormGrid>
        </FormSection>

        {/* Tipo de Publicidad */}
        <FormSection>
          <SectionTitle>
            <MegaphoneIcon />
            Tipo de Publicidad
          </SectionTitle>
          <FormGrid>
            <FormGroup>
              <Label>10. Tipo de Publicidad *</Label>
              <Select
                value={formData.advertisingType.type}
                onChange={(e) => handleInputChange('advertisingType', 'type', e.target.value)}
                $hasError={fieldErrors['advertisingType.type']}
              >
                <option value="">Seleccione tipo</option>
                {ADVERTISING_TYPES.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </Select>
            </FormGroup>
            <FormGroup>
              <Label>11. Descripción del medio publicitario *</Label>
              <Input
                type="text"
                value={formData.advertisingType.description}
                onChange={(e) => handleInputChange('advertisingType', 'description', e.target.value)}
                placeholder="Descripción detallada"
                $hasError={fieldErrors['advertisingType.description']}
              />
            </FormGroup>
            <FormGroup>
              <Label>12. Dimensiones y características físicas</Label>
              <Input
                type="text"
                value={formData.advertisingType.dimensions}
                onChange={(e) => handleInputChange('advertisingType', 'dimensions', e.target.value)}
                placeholder="Ej: 3m x 2m"
              />
            </FormGroup>
            <FormGroup>
              <Label>13. Área del elemento (Mts2) *</Label>
              <Input
                type="number"
                value={formData.advertisingType.area}
                onChange={(e) => handleInputChange('advertisingType', 'area', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                step="0.01"
                min="0"
                $hasError={fieldErrors['advertisingType.area']}
              />
            </FormGroup>
            <FormGroup>
              <Label>14. Número de elementos *</Label>
              <Input
                type="number"
                value={formData.advertisingType.numberOfElements}
                onChange={(e) => handleInputChange('advertisingType', 'numberOfElements', parseInt(e.target.value) || 0)}
                placeholder="0"
                min="0"
                $hasError={fieldErrors['advertisingType.numberOfElements']}
              />
            </FormGroup>
            <FormGroup>
              <Label>15.1. Duración de la publicidad - Inicio</Label>
              <Input
                type="date"
                value={formData.advertisingType.duration.start}
                onChange={(e) => handleNestedInputChange('advertisingType', 'duration', 'start', e.target.value)}
              />
            </FormGroup>
            <FormGroup>
              <Label>15.2. Duración de la publicidad - Fin</Label>
              <Input
                type="date"
                value={formData.advertisingType.duration.end}
                onChange={(e) => handleNestedInputChange('advertisingType', 'duration', 'end', e.target.value)}
              />
            </FormGroup>
            <FormGroup>
              <Label>16. Número de días</Label>
              <Input
                type="number"
                value={formData.advertisingType.numberOfDays}
                readOnly
                disabled
              />
            </FormGroup>
            <FormGroup>
              <Label>17. Número de promotores</Label>
              <Input
                type="number"
                value={formData.advertisingType.numberOfPromoters}
                onChange={(e) => handleInputChange('advertisingType', 'numberOfPromoters', parseInt(e.target.value) || 0)}
                placeholder="0"
                min="0"
              />
            </FormGroup>
            <FormGroup>
              <Label>18. Fracción *</Label>
              <Select
                value={formData.advertisingType.fraction}
                onChange={(e) => handleInputChange('advertisingType', 'fraction', e.target.value)}
                $hasError={fieldErrors['advertisingType.fraction']}
              >
                <option value="">Seleccione fracción</option>
                {FRACTIONS.map(fraction => (
                  <option key={fraction.value} value={fraction.value}>
                    {fraction.label}
                  </option>
                ))}
              </Select>
            </FormGroup>
            <FormGroup>
              <Label>19. Periodo de la Declaración *</Label>
              <Select
                value={formData.declaration.period}
                onChange={(e) => handleInputChange('declaration', 'period', e.target.value)}
                $hasError={fieldErrors['declaration.period']}
              >
                <option value="">Seleccione período</option>
                {PERIODS.map(period => (
                  <option key={period.value} value={period.value}>
                    {period.label}
                  </option>
                ))}
              </Select>
            </FormGroup>
            <FormGroup>
              <Label>20. Tipo de cambio (MMV) *</Label>
              <Select
                value={formData.declaration.exchangeType}
                onChange={(e) => handleInputChange('declaration', 'exchangeType', e.target.value)}
                $hasError={fieldErrors['declaration.exchangeType']}
              >
                <option value="">Seleccione tipo</option>
                {EXCHANGE_TYPES.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </Select>
            </FormGroup>
            <FormGroup>
              <Label>21. Monto Impuesto (Bs.)</Label>
              <Input
                type="text"
                value={formatCurrency(formData.declaration.taxAmount)}
                readOnly
                disabled
              />
            </FormGroup>
          </FormGrid>

          <AddButton onClick={addAdvertisingEntry}>
            <PlusIcon />
            Agregar Tipo de Publicidad
          </AddButton>
        </FormSection>

        {/* Tabla de Publicidad Registrada */}
        {advertisingEntries.length > 0 && (
          <TableSection>
            <SectionTitle>Publicidad Registrada</SectionTitle>
            <TableContainer>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHeaderCell>Tipo de Publicidad</TableHeaderCell>
                    <TableHeaderCell>Descripción del medio publicitario</TableHeaderCell>
                    <TableHeaderCell>Mts2</TableHeaderCell>
                    <TableHeaderCell>N° de elementos</TableHeaderCell>
                    <TableHeaderCell>N° de días</TableHeaderCell>
                    <TableHeaderCell>Fracción</TableHeaderCell>
                    <TableHeaderCell>Tipo de cambio (MMV)</TableHeaderCell>
                    <TableHeaderCell>Monto Impuesto (Bs.)</TableHeaderCell>
                    <TableHeaderCell>Acciones</TableHeaderCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {advertisingEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>{entry.type}</TableCell>
                      <TableCell>{entry.description}</TableCell>
                      <TableCell>{entry.area}</TableCell>
                      <TableCell>{entry.numberOfElements}</TableCell>
                      <TableCell>{entry.numberOfDays}</TableCell>
                      <TableCell>{entry.fraction}</TableCell>
                      <TableCell>{entry.exchangeType}</TableCell>
                      <TableCell>{formatCurrency(entry.taxAmount)}</TableCell>
                      <TableCell>
                        <DeleteButton onClick={() => removeAdvertisingEntry(entry.id)}>
                          <TrashIcon />
                        </DeleteButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TotalSection>
              <TotalLabel>Total a pagar:</TotalLabel>
              <TotalAmount>{formatCurrency(calculateTotal())}</TotalAmount>
            </TotalSection>
          </TableSection>
        )}

        {/* Submit Button */}
        <SubmitSection>
          <SubmitButton onClick={handleSubmit}>
            Enviar Registro
          </SubmitButton>
        </SubmitSection>
      </Content>

      {/* Confirmation Modal */}
      <AdvertisingConfirmationModal
        isOpen={isConfirmationModalOpen}
        onClose={() => setIsConfirmationModalOpen(false)}
        onConfirm={handleConfirmSubmission}
        data={getConfirmationData()}
        isLoading={isSubmitting}
      />
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  min-height: 100vh;
  background-color: ${props => props.theme.colors.background};
  transition: background-color 0.3s ease;
`;

const Header = styled.div`
  background-color: ${props => props.theme.colors.white};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  padding: ${props => props.theme.space[8]} ${props => props.theme.space[6]};
  transition: all 0.3s ease;
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.space[2]};
  transition: color 0.3s ease;
`;

const Subtitle = styled.p`
  font-size: ${props => props.theme.fontSizes.lg};
  color: ${props => props.theme.colors.textLight};
  margin: 0;
  transition: color 0.3s ease;
`;

const Content = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${props => props.theme.space[8]} ${props => props.theme.space[6]};
`;

const FormSection = styled.div`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  padding: ${props => props.theme.space[6]};
  margin-bottom: ${props => props.theme.space[6]};
  box-shadow: ${props => props.theme.shadows.sm};
  border: 1px solid ${props => props.theme.colors.borderLight};
  transition: all 0.3s ease;
`;

const TableSection = styled(FormSection)``;

const SectionTitle = styled.h2`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.space[6]};
  transition: color 0.3s ease;

  svg {
    width: 20px;
    height: 20px;
    color: ${props => props.theme.colors.primary};
  }
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.space[4]};

  @media (min-width: 640px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[2]};

  &.full-width {
    grid-column: 1 / -1;
  }
`;

const Label = styled.label`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const Input = styled.input<{ $hasError?: boolean }>`
  padding: ${props => props.theme.space[3]};
  border: 1px solid ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  background-color: ${props => props.theme.colors.white};
  color: ${props => props.theme.colors.text};
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.primary}20;
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.textLight};
    cursor: not-allowed;
  }

  &::placeholder {
    color: ${props => props.theme.colors.textLight};
  }
`;

const Select = styled.select<{ $hasError?: boolean }>`
  padding: ${props => props.theme.space[3]};
  border: 1px solid ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  background-color: ${props => props.theme.colors.white};
  color: ${props => props.theme.colors.text};
  cursor: pointer;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.primary}20;
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.textLight};
    cursor: not-allowed;
  }
`;

const AddButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  margin-top: ${props => props.theme.space[4]};
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.primaryDark};
  }

  svg {
    width: 20px;
    height: 20px;
  }
`;

const TableContainer = styled.div`
  overflow-x: auto;
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  transition: border-color 0.3s ease;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
`;

const TableHeader = styled.thead`
  background-color: ${props => props.theme.colors.backgroundMuted};
  transition: background-color 0.3s ease;
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const TableHeaderCell = styled.th`
  padding: ${props => props.theme.space[4]};
  text-align: left;
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  font-size: ${props => props.theme.fontSizes.sm};
  white-space: nowrap;
  transition: color 0.3s ease;
`;

const TableCell = styled.td`
  padding: ${props => props.theme.space[4]};
  color: ${props => props.theme.colors.text};
  font-size: ${props => props.theme.fontSizes.sm};
  transition: color 0.3s ease;
`;

const DeleteButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: ${props => props.theme.colors.danger};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.dangerDark};
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;

const TotalSection = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: ${props => props.theme.space[4]};
  padding: ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  margin-top: ${props => props.theme.space[4]};
  transition: background-color 0.3s ease;
`;

const TotalLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const TotalAmount = styled.span`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.primary};
  transition: color 0.3s ease;
`;

const SubmitSection = styled.div`
  display: flex;
  justify-content: center;
  margin-top: ${props => props.theme.space[8]};
`;

const SubmitButton = styled.button`
  padding: ${props => props.theme.space[4]} ${props => props.theme.space[8]};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.primaryDark};
  }
`;

export default Publicidad;
