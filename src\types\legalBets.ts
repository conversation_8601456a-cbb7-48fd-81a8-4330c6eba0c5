// src/types/legalBets.ts

export interface LegalBetFormData {
  contributor: {
    rif: string;
    nameOrBusinessName: string;
    accountNumber: string;
    phone: string;
    email: string;
    address: string;
    state: string;
    municipality: string;
  };
  declaration: {
    type: string; // Esquema de pago (Diario, Mensual, Semestral, Anual)
    month: number;
    year: number;
  };
  betDetails: {
    betType: string;
    betNumber: string;
    betAmount: number;
    taxRate: string;
    taxAmount: number;
  };
}

// Compatible interface for ConfirmationModal
export interface LegalBetRegistrationData {
  rif: string;
  nameOrBusinessName: string;
  accountNumber: string;
  phone: string;
  email: string;
  address: string;
  state: string;
  municipality: string;
  declarationType: string;
  month: number;
  year: number;
  betEntries: BetEntry[];
  totalAmount: number;
}

export interface BetEntry {
  id: string;
  betType: string;
  betTypeName: string;
  numberOfBets: number;
  betAmount: number;
  taxRate: string;
  taxAmount: number;
}

export const DECLARATION_TYPES = [
  { value: 'diario', label: 'Diario' },
  { value: 'mensual', label: 'Mensual' },
  { value: 'semestral', label: 'Semestral' },
  { value: 'anual', label: 'Anual' }
];

// Alícuotas por tipo de apuesta (ejemplo)
export const TAX_RATES = {
  loterias: '3%',
  quinielas_pronosticos: '5%',
  apuestas_casinos: '8%',
  apuestas_sorteos_suerte: '4%',
  apuestas_habilidad: '6%',
  apuestas_plataformas: '7%',
  bingo_otros: '3%'
};
