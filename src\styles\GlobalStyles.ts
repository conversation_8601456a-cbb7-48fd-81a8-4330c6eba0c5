// src/styles/GlobalStyles.ts
import { createGlobalStyle } from 'styled-components';

export const GlobalStyles = createGlobalStyle`
  *, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  @font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
  }

  html, body {
    height: 100%;
    width: 100%;
    font-family: ${props => props.theme.fonts.body};
    font-size: 14px; /* Radix UI uses 14px as base font size */
    line-height: ${props => props.theme.lineHeights.normal};
    background-color: ${props => props.theme.colors.background};
    color: ${props => props.theme.colors.text};
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  #root {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  a {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;
    transition: ${props => props.theme.transitions.default};

    &:hover {
      color: ${props => props.theme.colors.primaryHover};
      text-decoration: underline;
    }
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: ${props => props.theme.fontWeights.semibold};
    line-height: ${props => props.theme.lineHeights.heading};
    color: ${props => props.theme.colors.text};
    margin-bottom: ${props => props.theme.space[3]};
  }

  h1 {
    font-size: ${props => props.theme.fontSizes['4xl']};
  }

  h2 {
    font-size: ${props => props.theme.fontSizes['3xl']};
  }

  h3 {
    font-size: ${props => props.theme.fontSizes['2xl']};
  }

  h4 {
    font-size: ${props => props.theme.fontSizes.xl};
  }

  h5 {
    font-size: ${props => props.theme.fontSizes.lg};
  }

  h6 {
    font-size: ${props => props.theme.fontSizes.md};
  }

  p {
    margin-bottom: ${props => props.theme.space[3]};
  }

  button, input, select, textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }

  button {
    cursor: pointer;
    border: none;
    background: none;

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  ul, ol {
    list-style: none;
  }

  img, svg {
    vertical-align: middle;
  }

  code {
    font-family: ${props => props.theme.fonts.monospace};
    font-size: ${props => props.theme.fontSizes.sm};
    line-height: ${props => props.theme.lineHeights.code};
    background-color: ${props => props.theme.colors.borderLight};
    padding: ${props => props.theme.space[1]} ${props => props.theme.space[2]};
    border-radius: ${props => props.theme.radii.sm};
  }

  /* Scrollbar styles - Radix UI style */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.textMuted};
    border: 3px solid ${props => props.theme.colors.background};
    border-radius: ${props => props.theme.radii.full};
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${props => props.theme.colors.textLight};
  }

  /* Focus styles for accessibility - Radix UI style */
  :focus-visible {
    outline: none;
    box-shadow: ${props => props.theme.shadows.focus};
    border-radius: ${props => props.theme.radii.sm};
  }
`;
