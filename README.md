# Taxia - Sistema de Gestión Tributaria Municipal

Taxia es una aplicación web moderna para la gestión tributaria municipal, desarrollada con React, TypeScript y Radix UI.

## Requisitos previos

Antes de instalar y ejecutar Taxia, asegúrate de tener instalado:

- [Node.js](https://nodejs.org/) (versión 18.0.0 o superior)
- [npm](https://www.npmjs.com/) (normalmente viene con Node.js)

## Instalación

Sigue estos pasos para instalar y configurar Taxia:

1. **Clonar el repositorio**

```bash
git clone https://github.com/casz92/taxlite.git
cd taxia
```

2. **Instalar dependencias**

```bash
npm install
```

Si prefieres instalar las dependencias manualmente, puedes usar los siguientes comandos:

```bash
# Dependencias principales
npm install react@19.1.0 react-dom@19.1.0 react-router-dom@7.6.0 styled-components@6.1.18

# Componentes Radix UI
npm install @radix-ui/react-accordion @radix-ui/react-avatar @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-navigation-menu @radix-ui/react-tabs @radix-ui/react-toast @radix-ui/react-icons

# HeroIcons
npm install @heroicons/react

# Dependencias de desarrollo
npm install -D typescript@5.8.3 vite@6.3.5 @vitejs/plugin-react
```

## Dependencias principales

Taxia utiliza las siguientes dependencias principales:

- **React** (v19.1.0): Biblioteca para construir interfaces de usuario
- **TypeScript** (v5.8.3): Superset tipado de JavaScript
- **Vite** (v6.3.5): Herramienta de construcción rápida para desarrollo web
- **Radix UI**: Biblioteca de componentes UI accesibles y sin estilos
  - @radix-ui/react-accordion (v1.2.10)
  - @radix-ui/react-avatar (v1.1.9)
  - @radix-ui/react-dialog (v1.1.13)
  - @radix-ui/react-dropdown-menu (v2.1.14)
  - @radix-ui/react-navigation-menu (v1.2.12)
  - @radix-ui/react-tabs (v1.1.11)
  - @radix-ui/react-toast (v1.2.13)
- **Styled Components** (v6.1.18): Biblioteca para estilizar componentes con CSS-in-JS
- **React Router** (v7.6.0): Biblioteca para manejo de rutas en aplicaciones React
- **HeroIcons** (v2.2.0): Conjunto de iconos SVG

## Ejecución

### Modo desarrollo

Para ejecutar la aplicación en modo desarrollo:

```bash
npm run dev
```

Esto iniciará el servidor de desarrollo en `http://localhost:5173` (o en el siguiente puerto disponible si el 5173 está ocupado).

### Construcción para producción

Para construir la aplicación para producción:

```bash
npm run build
```

Los archivos de salida se generarán en el directorio `dist/`.

### Vista previa de producción

Para previsualizar la versión de producción localmente:

```bash
npm run preview
```

## Estructura del proyecto

```
taxia/
├── public/             # Archivos estáticos
├── src/                # Código fuente
│   ├── components/     # Componentes reutilizables
│   ├── layouts/        # Componentes de estructura
│   ├── pages/          # Páginas de la aplicación
│   ├── styles/         # Estilos globales y temas
│   ├── App.tsx         # Componente principal
│   └── main.tsx        # Punto de entrada
├── index.html          # Plantilla HTML
├── package.json        # Dependencias y scripts
├── tsconfig.json       # Configuración de TypeScript
└── vite.config.ts      # Configuración de Vite
```

## Características principales

- Interfaz de usuario moderna y accesible
- Menú lateral con navegación jerárquica
- Componentes UI accesibles con Radix UI
- Estilos personalizables con Styled Components
- Navegación entre páginas con React Router
- Títulos de página dinámicos

## Solución de problemas comunes

### El servidor no inicia en el puerto 5173

Si ves un mensaje como "Port 5173 is in use, trying another one...", significa que el puerto predeterminado está ocupado. Vite automáticamente intentará usar el siguiente puerto disponible. Puedes especificar un puerto manualmente con:

```bash
npm run dev --port 3000
# O para exponerlo a la red local
npm run dev -- --port 3000
```

### Errores de dependencias

Si encuentras errores relacionados con dependencias incompatibles, intenta:

```bash
npm clean-cache --force
rm -rf node_modules
npm install
```

### Problemas con Styled Components

Si los estilos no se aplican correctamente, asegúrate de que el ThemeProvider esté envolviendo correctamente tu aplicación en `App.tsx`.

### Errores de TypeScript

Para resolver errores de tipos, ejecuta:

```bash
npx tsc --noEmit
```

Esto verificará los tipos sin generar archivos JavaScript.
