// src/hooks/useResponsiveSidebar.ts
import { useState, useEffect, useCallback } from 'react';

interface UseResponsiveSidebarReturn {
  isSidebarCollapsed: boolean;
  isMobile: boolean;
  isOverlayVisible: boolean;
  toggleSidebar: () => void;
  closeSidebar: () => void;
}

const SIDEBAR_STORAGE_KEY = 'taxia-sidebar-collapsed';
const MOBILE_BREAKPOINT = 768; // matches tablet breakpoint

export const useResponsiveSidebar = (): UseResponsiveSidebarReturn => {
  // Initialize state from localStorage or default values
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    if (typeof window === 'undefined') return false;

    const stored = localStorage.getItem(SIDEBAR_STORAGE_KEY);
    return stored ? JSON.parse(stored) : false;
  });

  const [isMobile, setIsMobile] = useState(() => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth < MOBILE_BREAKPOINT;
  });

  // On mobile, overlay is visible when sidebar is expanded
  // On desktop, no overlay is needed
  const isOverlayVisible = isMobile && !isSidebarCollapsed;

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < MOBILE_BREAKPOINT;
      const wasMobile = isMobile;
      setIsMobile(mobile);

      // Only auto-collapse when switching FROM desktop TO mobile
      // Don't auto-collapse if already on mobile
      if (!wasMobile && mobile && !isSidebarCollapsed) {
        setIsSidebarCollapsed(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobile, isSidebarCollapsed]);

  // Save to localStorage whenever sidebar state changes
  useEffect(() => {
    localStorage.setItem(SIDEBAR_STORAGE_KEY, JSON.stringify(isSidebarCollapsed));
  }, [isSidebarCollapsed]);

  // Toggle sidebar state
  const toggleSidebar = useCallback(() => {
    setIsSidebarCollapsed(prev => !prev);
  }, []);

  // Close sidebar (useful for mobile overlay clicks)
  const closeSidebar = useCallback(() => {
    setIsSidebarCollapsed(true);
  }, []);

  // Auto-collapse on mobile ONLY when component first mounts
  useEffect(() => {
    // Only run on initial mount, not on every isMobile change
    if (isMobile && !isSidebarCollapsed) {
      setIsSidebarCollapsed(true);
    }
  }, []); // Empty dependency array - only runs on mount

  // Close sidebar when clicking outside, but only on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !isSidebarCollapsed) { // <--  Only close on mobile
        const target = event.target as Element;
        // Check if click is outside sidebar and not on the menu button
        if (!target.closest('aside') && !target.closest('[data-menu-button]')) {
          setIsSidebarCollapsed(true);
        }
      }
    };

    if (isMobile && !isSidebarCollapsed) { // <-- Only listen on mobile
      // Use a timeout to avoid immediate triggering after opening
      const timeoutId = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside);
      }, 150); // Increased timeout for better mobile experience

      return () => {
        clearTimeout(timeoutId);
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isMobile, isSidebarCollapsed]);

  // Prevent body scroll when mobile sidebar is open
  useEffect(() => {
    if (isMobile && !isSidebarCollapsed) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [isMobile, isSidebarCollapsed]);

  // Close sidebar with Escape key
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && !isSidebarCollapsed) {
        setIsSidebarCollapsed(true);
      }
    };

    if (!isSidebarCollapsed) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isSidebarCollapsed]);

  return {
    isSidebarCollapsed,
    isMobile,
    isOverlayVisible,
    toggleSidebar,
    closeSidebar,
  };
};