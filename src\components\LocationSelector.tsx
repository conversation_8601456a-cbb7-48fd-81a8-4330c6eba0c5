// src/components/LocationSelector.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import * as Dialog from '@radix-ui/react-dialog';
import { MapContainer as LeafletMap, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import { MapPinIcon, XMarkIcon, CheckIcon } from '@heroicons/react/24/outline';
import type { Location } from '../types/economicActivity';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

interface LocationSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (location: Location) => void;
  currentLocation?: Location | null;
}

// Component to handle map clicks
interface MapClickHandlerProps {
  onLocationSelect: (location: Location) => void;
}

const MapClickHandler: React.FC<MapClickHandlerProps> = ({ onLocationSelect }) => {
  useMapEvents({
    click: (e) => {
      const { lat, lng } = e.latlng;
      const location: Location = {
        latitude: parseFloat(lat.toFixed(6)),
        longitude: parseFloat(lng.toFixed(6)),
        address: `Lat: ${lat.toFixed(6)}, Lng: ${lng.toFixed(6)}`
      };
      onLocationSelect(location);
    }
  });
  return null;
};

const LocationSelector: React.FC<LocationSelectorProps> = ({
  isOpen,
  onClose,
  onSelect,
  currentLocation
}) => {
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(currentLocation || null);
  const [isLoading, setIsLoading] = useState(false);

  // Default center for Venezuela (Caracas)
  const defaultCenter: [number, number] = [10.4806, -66.9036];
  const defaultZoom = 10;

  // Reset selected location when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedLocation(currentLocation || null);
    }
  }, [isOpen, currentLocation]);

  // Get user's current location
  const getCurrentLocation = () => {
    setIsLoading(true);

    if (!navigator.geolocation) {
      alert('La geolocalización no está soportada por este navegador.');
      setIsLoading(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const location: Location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          address: 'Ubicación actual'
        };
        setSelectedLocation(location);
        setIsLoading(false);
      },
      (error) => {
        console.error('Error getting location:', error);
        alert('No se pudo obtener la ubicación actual. Por favor, seleccione manualmente.');
        setIsLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    );
  };

  // Handle location selection from map click
  const handleLocationSelect = (location: Location) => {
    setSelectedLocation(location);
  };

  const handleConfirm = () => {
    if (selectedLocation) {
      onSelect(selectedLocation);
      onClose();
    }
  };

  const handleClose = () => {
    setSelectedLocation(currentLocation || null);
    onClose();
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleClose}>
      <Dialog.Portal>
        <DialogOverlay />
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Seleccionar Ubicación</DialogTitle>
            <DialogClose onClick={handleClose}>
              <XMarkIcon />
            </DialogClose>
          </DialogHeader>

          <DialogBody>
            <LocationInfo>
              <InfoText>
                Haga clic en el mapa para seleccionar la ubicación del contribuyente o use su ubicación actual.
              </InfoText>

              <CurrentLocationButton
                onClick={getCurrentLocation}
                disabled={isLoading}
              >
                <MapPinIcon />
                {isLoading ? 'Obteniendo ubicación...' : 'Usar ubicación actual'}
              </CurrentLocationButton>
            </LocationInfo>

            <MapWrapper>
              <LeafletMap
                center={selectedLocation ? [selectedLocation.latitude, selectedLocation.longitude] : defaultCenter}
                zoom={defaultZoom}
                style={{ height: '100%', width: '100%' }}
              >
                <TileLayer
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                />
                <MapClickHandler onLocationSelect={handleLocationSelect} />
                {selectedLocation && (
                  <Marker position={[selectedLocation.latitude, selectedLocation.longitude]} />
                )}
              </LeafletMap>
            </MapWrapper>

            {selectedLocation && (
              <SelectedLocationInfo>
                <LocationLabel>Ubicación seleccionada:</LocationLabel>
                <LocationDetails>
                  <LocationDetail>
                    <strong>Latitud:</strong> {selectedLocation.latitude}
                  </LocationDetail>
                  <LocationDetail>
                    <strong>Longitud:</strong> {selectedLocation.longitude}
                  </LocationDetail>
                  {selectedLocation.address && (
                    <LocationDetail>
                      <strong>Dirección:</strong> {selectedLocation.address}
                    </LocationDetail>
                  )}
                </LocationDetails>
              </SelectedLocationInfo>
            )}
          </DialogBody>

          <DialogFooter>
            <CancelButton onClick={handleClose}>
              Cancelar
            </CancelButton>
            <ConfirmButton
              onClick={handleConfirm}
              disabled={!selectedLocation}
            >
              <CheckIcon />
              Confirmar Ubicación
            </ConfirmButton>
          </DialogFooter>
        </DialogContent>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

// Styled Components
const DialogOverlay = styled(Dialog.Overlay)`
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  inset: 0;
  z-index: 50;
`;

const DialogContent = styled(Dialog.Content)`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  box-shadow: ${props => props.theme.shadows.xl};
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 800px;
  max-height: 85vh;
  padding: 0;
  z-index: 51;
  display: flex;
  flex-direction: column;
`;

const DialogHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.space[6]};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const DialogTitle = styled(Dialog.Title)`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const DialogClose = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: ${props => props.theme.radii.md};
  border: none;
  background-color: transparent;
  color: ${props => props.theme.colors.textLight};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 20px;
    height: 20px;
  }

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.text};
  }
`;

const DialogBody = styled.div`
  flex: 1;
  padding: ${props => props.theme.space[6]};
  overflow: auto;
`;

const LocationInfo = styled.div`
  margin-bottom: ${props => props.theme.space[4]};
`;

const InfoText = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  margin: 0 0 ${props => props.theme.space[3]};
`;

const CurrentLocationButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[4]};
  border: 1px solid ${props => props.theme.colors.primary};
  border-radius: ${props => props.theme.radii.md};
  background-color: transparent;
  color: ${props => props.theme.colors.primary};
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 16px;
    height: 16px;
  }

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.primary};
    color: white;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const MapWrapper = styled.div`
  width: 100%;
  height: 400px;
  border: 2px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  overflow: hidden;
  margin-bottom: ${props => props.theme.space[4]};

  .leaflet-container {
    height: 100%;
    width: 100%;
  }
`;

const SelectedLocationInfo = styled.div`
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  padding: ${props => props.theme.space[4]};
`;

const LocationLabel = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.space[2]};
`;

const LocationDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[1]};
`;

const LocationDetail = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
`;

const DialogFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${props => props.theme.space[3]};
  padding: ${props => props.theme.space[6]};
  border-top: 1px solid ${props => props.theme.colors.borderLight};
`;

const CancelButton = styled.button`
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[4]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  background-color: transparent;
  color: ${props => props.theme.colors.text};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted};
  }
`;

const ConfirmButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[4]};
  border: 1px solid ${props => props.theme.colors.primary};
  border-radius: ${props => props.theme.radii.md};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 16px;
    height: 16px;
  }

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.primaryHover};
    border-color: ${props => props.theme.colors.primaryHover};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

export default LocationSelector;