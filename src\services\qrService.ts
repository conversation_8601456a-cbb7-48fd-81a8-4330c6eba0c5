// src/services/qrService.ts
import QrScanner from 'qr-scanner';
import Fuse from 'fuse.js';
import distance from 'jaro-winkler';

export interface QRResult {
  data: string;
  success: boolean;
}

export interface ExtractedData {
  rifNumber?: string;
  nameOrBusinessName?: string;
  plateNumber?: string;
  chassisNumber?: string;
  engineNumber?: string;
  brand?: string;
  model?: string;
  year?: number;
  color?: string;
  vehicleType?: string;
  vehicleUseType?: string;
  confidence: number;
}

export interface QRProgress {
  status: string;
  progress: number;
}

class QRService {
  // Scan QR code from image file
  async scanQRFromImage(
    file: File,
    onProgress?: (progress: QRProgress) => void
  ): Promise<QRResult> {
    try {
      // Report initial progress
      if (onProgress) {
        onProgress({ status: 'Iniciando lectura de QR...', progress: 10 });
      }

      // Create image element from file
      const imageUrl = URL.createObjectURL(file);
      const img = new Image();

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = imageUrl;
      });

      // Report progress
      if (onProgress) {
        onProgress({ status: 'Procesando imagen...', progress: 50 });
      }

      // Scan QR code
      const qrData = await QrScanner.scanImage(img);

      // Clean up
      URL.revokeObjectURL(imageUrl);

      // Report completion
      if (onProgress) {
        onProgress({ status: 'QR leído exitosamente', progress: 100 });
      }

      return {
        data: qrData as string,
        success: true
      };

    } catch (error) {
      console.error('QR Scan Error:', error);
      throw new Error('No se pudo leer el código QR de la imagen');
    }
  }

  // Extract structured data from QR content
  extractStructuredData(qrData: string): ExtractedData {
    const extractedData: ExtractedData = {
      confidence: 0
    };

    try {
      // Try to parse as JSON first (common QR format)
      const jsonData = JSON.parse(qrData);

      if (typeof jsonData === 'object') {
        // Map common JSON fields to our structure
        this.mapJsonFields(jsonData, extractedData);
      }
    } catch {
      // If not JSON, try to parse as structured text
      this.parseStructuredText(qrData, extractedData);
    }

    // Calculate confidence based on extracted fields
    const extractedFields = Object.keys(extractedData).filter(key =>
      key !== 'confidence' && extractedData[key as keyof ExtractedData]
    ).length;

    extractedData.confidence = Math.min(100, (extractedFields / 10) * 100);

    return extractedData;
  }

  // Map JSON fields to our data structure
  private mapJsonFields(jsonData: any, extractedData: ExtractedData): void {
    // Common field mappings for vehicle documents
    const fieldMappings = {
      // RIF/Document mappings
      rif: ['rif', 'cedula', 'documento', 'doc', 'identification', 'CɄULA'],
      rifNumber: ['rifNumber', 'documentNumber', 'idNumber'],

      // Name mappings
      name: ['nombre', 'name', 'propietario', 'owner', 'titular'],
      nameOrBusinessName: ['nameOrBusinessName', 'razonSocial', 'businessName'],

      // Vehicle mappings
      plate: ['placa', 'plate', 'plateNumber', 'numeroPlaca'],
      chassis: ['chasis', 'chassis', 'vin', 'numeroChasis'],
      engine: ['motor', 'engine', 'numeroMotor', 'engineNumber'],
      brand: ['marca', 'brand', 'fabricante'],
      model: ['modelo', 'model'],
      year: ['año', 'year', 'Aя'],
      color: ['color', 'colour'],

      // Vehicle type mappings
      vehicleType: ['tipoVehiculo', 'vehicleType', 'tipo'],
      vehicleUseType: ['usoVehiculo', 'vehicleUseType', 'uso']
    };

    // Apply mappings
    for (const [targetField, sourceFields] of Object.entries(fieldMappings)) {
      for (const sourceField of sourceFields) {
        if (jsonData[sourceField] !== undefined) {
          const value = jsonData[sourceField];

          if (targetField === 'year') {
            const yearValue = parseInt(value);
            if (yearValue >= 1900 && yearValue <= new Date().getFullYear() + 1) {
              extractedData.year = yearValue;
            }
          } else {
            (extractedData as any)[targetField] = this.cleanText(String(value));
          }
          break;
        }
      }
    }
  }

  // Parse structured text (key:value pairs)
  private parseStructuredText(qrData: string, extractedData: ExtractedData): void {
    const lines = qrData.split(/[\n\r|;]/).map(line => line.trim()).filter(line => line.length > 0);

    for (const line of lines) {
      // Try to find key:value patterns
      const colonMatch = line.match(/^([^:]+):\s*(.+)$/);
      const equalMatch = line.match(/^([^=]+)=\s*(.+)$/);

      const match = colonMatch || equalMatch;
      if (match) {
        const key = match[1].trim().toLowerCase();
        const value = match[2].trim();
        console.log(key, value);
        // Map keys to our fields
        if (this.matchesField(key, ['rif', 'cedula', 'documento', 'doc'])) {
          extractedData.rifNumber = this.cleanText(value);
        } else if (this.matchesField(key, ['nombre', 'name', 'propietario', 'titular'])) {
          extractedData.nameOrBusinessName = this.cleanText(value);
        } else if (this.matchesField(key, ['placa', 'plate'])) {
          extractedData.plateNumber = this.cleanText(value);
        } else if (this.matchesField(key, ['chasis', 'chassis', 'vin'])) {
          extractedData.chassisNumber = this.cleanText(value);
        } else if (this.matchesField(key, ['motor', 'engine'])) {
          extractedData.engineNumber = this.cleanText(value);
        } else if (this.matchesField(key, ['marca', 'brand'])) {
          extractedData.brand = this.cleanText(value);
        } else if (this.matchesField(key, ['modelo', 'model'])) {
          extractedData.model = this.cleanText(value);
        } else if (this.matchesField(key, ['año', 'year', 'aя'])) {
          console.log('Found year:', value);
          const year = parseInt(value);
          if (year >= 1900 && year <= new Date().getFullYear() + 1) {
            extractedData.year = year;
          }
        } else if (this.matchesField(key, ['color', 'colour'])) {
          extractedData.color = this.cleanText(value);
        }
      }
    }
  }

  // Check if a key matches any of the expected field names
  private matchesField(key: string, fieldNames: string[]): boolean {
    return fieldNames.some((field) => {
      return key.includes(field) || field.includes(key) || distance(key, field) >= 0.8;
    });
  }

  // Clean extracted text
  private cleanText(text: string): string {
    return text
      .replace(/[^\w\s\-]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
      .toUpperCase();
  }

  // Find best match for select options using fuzzy search
  findBestMatch(extractedValue: string, options: Array<{ id: string, name: string }>): string | null {
    if (!extractedValue || !options.length) return null;

    const fuse = new Fuse(options, {
      keys: ['name'],
      threshold: 0.4, // Adjust threshold for fuzzy matching
      includeScore: true
    });

    const results = fuse.search(extractedValue);

    if (results.length > 0 && results[0].score! < 0.4) {
      return results[0].item.id;
    }

    return null;
  }
}

export const qrService = new QRService();
