// src/pages/NotFound.tsx
import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { ExclamationTriangleIcon, HomeIcon } from '@radix-ui/react-icons';
import { theme } from '../styles/theme';

const NotFound: React.FC = () => {
  return (
    <NotFoundContainer>
      <NotFoundContent>
        <ErrorIcon>
          <ExclamationTriangleIcon width={48} height={48} />
        </ErrorIcon>
        <ErrorCode>404</ErrorCode>
        <ErrorTitle>Page Not Found</ErrorTitle>
        <ErrorMessage>
          The page you are looking for might have been removed, had its name changed, 
          or is temporarily unavailable.
        </ErrorMessage>
        <HomeButton to="/">
          <HomeIcon />
          <span>Back to Home</span>
        </HomeButton>
      </NotFoundContent>
    </NotFoundContainer>
  );
};

// Styled Components
const NotFoundContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: ${props => props.theme.colors.background};
  padding: ${props => props.theme.space[4]};
`;

const NotFoundContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 480px;
  padding: ${props => props.theme.space[6]};
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  box-shadow: ${props => props.theme.shadows.md};
`;

const ErrorIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: ${props => props.theme.radii.full};
  background-color: ${props => props.theme.colors.danger}20;
  color: ${props => props.theme.colors.danger};
  margin-bottom: ${props => props.theme.space[4]};
`;

const ErrorCode = styled.div`
  font-size: ${props => props.theme.fontSizes['5xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.space[2]};
`;

const ErrorTitle = styled.h1`
  font-size: ${props => props.theme.fontSizes['2xl']};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.space[4]};
`;

const ErrorMessage = styled.p`
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.textLight};
  margin-bottom: ${props => props.theme.space[6]};
`;

const HomeButton = styled(Link)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.primary};
  color: ${props => props.theme.colors.white};
  font-weight: ${props => props.theme.fontWeights.medium};
  border-radius: ${props => props.theme.radii.md};
  transition: ${props => props.theme.transitions.default};
  
  &:hover {
    background-color: ${props => props.theme.colors.primaryHover};
    color: ${props => props.theme.colors.white};
  }
`;

export default NotFound;
