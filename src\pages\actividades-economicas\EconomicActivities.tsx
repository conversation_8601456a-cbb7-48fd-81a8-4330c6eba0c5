// src/pages/actividades-economicas/EconomicActivities.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import * as Form from '@radix-ui/react-form';
import {
  UserIcon,
  DocumentTextIcon,
  MapPinIcon,
  BriefcaseIcon,
  PaperClipIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import { useToast } from '../../components/ToastProvider';
import EconomicActivityModal from '../../components/EconomicActivityModal';
import LocationSelector from '../../components/LocationSelector';
import FileUpload from '../../components/FileUpload';
import LoadingOverlay from '../../components/LoadingOverlay';
import EconomicActivityConfirmationModal from '../../components/EconomicActivityConfirmationModal';
import type {
  EconomicActivityFormData,
  Declaration,
  Location,
  EconomicActivity,
} from '../../types/economicActivity';

// Preloaded activities data
const PRELOADED_ACTIVITIES = [
  {
    number: "010002",
    code: "2.1.07",
    descri: "Fabricación de bebidas no alcohólicas",
    alicuota: "1.2%",
    mmv: 20,
    montoBase: 0
  },
  {
    number: "010005",
    code: "2.2.05",
    descri: "Comercio al por menor de productos farmacéuticos",
    alicuota: "1.5%",
    mmv: 30,
    montoBase: 0
  },
  {
    number: "010007",
    code: "2.3.05",
    descri: "Servicios de restaurantes y cafeterías",
    alicuota: "1.8%",
    mmv: 28,
    montoBase: 0
  }
];

interface ActivityTableRow extends EconomicActivity {
  montoBase: number;
}

import { DECLARATION_TYPES, getAvailableYears } from '../../types/economicActivity';
import { MONTHS } from '../../types/misc';

const EconomicActivities: React.FC = () => {
  const { showToast } = useToast();

  // Form state
  const [formData, setFormData] = useState<EconomicActivityFormData>({
    contributor: {
      nameOrBusinessName: 'Juan Carlos Pérez Rodríguez',
      licenseNumber: 'LIC-2024-001234',
      rif: '**********-9',
      email: '<EMAIL>',
      legalResponsible: 'Juan Carlos Pérez Rodríguez',
      localPhone: '0212-1234567',
      cellPhone: '0414-1234567',
      address: 'Av. Principal, Edificio Torre Centro, Piso 5, Oficina 501, Caracas, Venezuela'
    },
    declaration: {
      type: 'definitiva',
      year: new Date().getFullYear(),
      month: new Date().getMonth() + 1
    },
    location: null,
    selectedActivity: null,
    ivaDeclaration: null,
    ivaRelationDeclaration: null
  });

  // UI state
  const [isActivityModalOpen, setIsActivityModalOpen] = useState(false);
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Activities table state
  const [activities, setActivities] = useState<ActivityTableRow[]>(PRELOADED_ACTIVITIES);

  // Track which input is being edited to show raw value instead of formatted
  const [editingInput, setEditingInput] = useState<string | null>(null);

  // Available years for declaration
  const availableYears = getAvailableYears();

  // Handle declaration changes
  const handleDeclarationChange = (field: keyof Declaration, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      declaration: {
        ...prev.declaration,
        [field]: value
      }
    }));

    // Clear error when user makes changes
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle activity selection
  const handleActivitySelect = (activity: EconomicActivity) => {
    setFormData(prev => ({
      ...prev,
      selectedActivity: activity
    }));

    // Clear error
    if (errors.selectedActivity) {
      setErrors(prev => ({ ...prev, selectedActivity: '' }));
    }
  };

  // Handle location selection
  const handleLocationSelect = (location: Location) => {
    setFormData(prev => ({
      ...prev,
      location
    }));

    // Clear error
    if (errors.location) {
      setErrors(prev => ({ ...prev, location: '' }));
    }
  };

  // Handle file uploads
  const handleFileSelect = (field: 'ivaDeclaration' | 'ivaRelationDeclaration') => (file: File | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: file
    }));

    // Clear error
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Format number as currency for display
  const formatCurrency = (value: number): string => {
    if (value === 0) return '';
    return new Intl.NumberFormat('es-VE', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Format number for editing (preserves decimals)
  const formatForEditing = (value: number): string => {
    if (value === 0) return '';
    // Keep the number as is for editing, allowing decimals
    return value.toString();
  };

  // Parse input string to number (supports both comma and dot as decimal separator)
  const parseInputValue = (value: string): number => {
    // Si el valor está vacío o solo contiene espacios, retorna 0.
    if (!value || value.trim() === '') {
      return 0;
    }

    // Permite solo dígitos, puntos, comas y el signo de menos.
    // Elimina cualquier otro carácter.
    let cleanValue = value.replace(/[^\d.,]/g, '');

    // Encuentra la última aparición de una coma y un punto.
    const lastCommaIndex = cleanValue.lastIndexOf(',');
    const lastDotIndex = cleanValue.lastIndexOf('.');

    // Lógica para determinar el separador decimal:
    // Si la última coma aparece después del último punto, se asume que la coma es el separador decimal.
    if (lastCommaIndex > lastDotIndex) {
      // Reemplaza esa última coma por un punto para estandarizar el separador decimal.
      cleanValue = cleanValue.substring(0, lastCommaIndex) + '.' + cleanValue.substring(lastCommaIndex + 1);
      // Elimina todas las demás comas (considerándolas separadores de miles).
      cleanValue = cleanValue.replace(/,/g, '');
    } else {
      // Si el punto es el último o no hay comas, se eliminan todas las comas (considerándolas separadores de miles).
      cleanValue = cleanValue.replace(/,/g, '');
    }

    // --- INICIO DE LA CORRECCIÓN ---
    // Maneja múltiples puntos:
    // Divide la cadena por el punto decimal.
    const dotParts = cleanValue.split('.');
    if (dotParts.length > 1) {
      // Si hay más de una parte (lo que significa que hay al menos un punto),
      // mantenemos la primera parte (el entero)
      // y concatenamos un punto con el resto de las partes unidas.
      // Esto asegura que solo el PRIMER punto funcione como separador decimal,
      // y evita que "1.2." se convierta en "12." (ahora será "1.2").
      cleanValue = dotParts[0] + '.' + dotParts.slice(1).join('');
    }
    // --- FIN DE LA CORRECCIÓN ---

    // Intenta analizar la cadena limpia como un número flotante.
    const parsed = parseFloat(cleanValue);

    // Retorna el número analizado. Si no es un número válido (NaN), retorna 0.
    // También se asegura de que el número sea no negativo, tomando el máximo entre 0 y el número.
    return isNaN(parsed) ? 0 : Math.max(0, parsed);
  };

  // Handle monto base change in activities table
  const handleMontoBaseChange = (activityNumber: string, value: string) => {
    // Allow typing decimal numbers without immediate parsing
    const numericValue = parseInputValue(value);
    console.log(`Monto base changed for activity ${activityNumber}: ${value} -> ${numericValue}`);

    setActivities(prev =>
      prev.map(activity =>
        activity.number === activityNumber
          ? { ...activity, montoBase: numericValue }
          : activity
      )
    );
  };

  // Handle key press to allow decimal input
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const char = e.key;
    const value = (e.target as HTMLInputElement).value;

    // Allow: backspace, delete, tab, escape, enter, arrow keys
    if (['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(char) ||
      // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
      (char === 'a' && e.ctrlKey) ||
      (char === 'c' && e.ctrlKey) ||
      (char === 'v' && e.ctrlKey) ||
      (char === 'x' && e.ctrlKey)) {
      return;
    }

    // Allow: numbers, decimal point, comma
    if (!/[\d.,]/.test(char)) {
      e.preventDefault();
      return;
    }

    // Allow only one decimal separator
    if ((char === '.' || char === ',') && (value.includes('.') || value.includes(','))) {
      e.preventDefault();
    }
  };

  // Calculate tax amount based on monto base and alicuota
  const calculateTax = (montoBase: number, alicuota: string): number => {
    const alicuotaValue = parseFloat(alicuota.replace('%', '')) / 100;
    return montoBase * alicuotaValue;
  };

  // Calculate total declared amount (sum of all taxes)
  const calculateTotalDeclared = (): number => {
    return activities.reduce((total, activity) => {
      return total + calculateTax(activity.montoBase, activity.alicuota);
    }, 0);
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate declaration
    if (!formData.declaration.type) {
      newErrors.declarationType = 'Debe seleccionar el tipo de declaración';
    }

    if (!formData.declaration.year) {
      newErrors.declarationYear = 'Debe seleccionar el año';
    }

    if (!formData.declaration.month) {
      newErrors.declarationMonth = 'Debe seleccionar el mes';
    }

    // Validate location
    if (!formData.location) {
      newErrors.location = 'Debe seleccionar la ubicación del contribuyente';
    }

    // Validate activities (at least one activity should have monto base > 0)
    const hasValidActivity = activities.some(activity => activity.montoBase > 0);
    if (!hasValidActivity) {
      newErrors.activities = 'Debe ingresar al menos un monto base mayor a 0';
    }

    // Validate required documents
    if (!formData.ivaDeclaration) {
      newErrors.ivaDeclaration = 'Debe cargar la declaración de IVA del periodo';
    }

    if (!formData.ivaRelationDeclaration) {
      newErrors.ivaRelationDeclaration = 'Debe cargar la declaración de IVA relación de ingreso';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Reset editable form fields to initial state
  const resetEditableFields = () => {
    setFormData(prev => ({
      ...prev,
      declaration: {
        type: 'definitiva',
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1
      },
      location: null,
      selectedActivity: null,
      ivaDeclaration: null,
      ivaRelationDeclaration: null
    }));

    // Reset activities table to zero amounts
    setActivities(PRELOADED_ACTIVITIES.map(activity => ({
      ...activity,
      montoBase: 0
    })));

    // Clear editing state
    setEditingInput(null);

    // Clear all validation errors
    setErrors({});
  };

  // Handle form submission (show confirmation modal)
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      showToast('Formulario incompleto', 'Por favor complete todos los campos requeridos', 'error');
      return;
    }

    // Show confirmation modal instead of submitting directly
    setShowConfirmModal(true);
  };

  // Handle confirmed submission
  const handleConfirmedSubmit = async () => {
    setIsLoading(true);

    try {
      // Prepare activities data with non-zero amounts
      const activitiesWithAmounts = activities
        .filter(activity => activity.montoBase > 0)
        .map(activity => ({
          code: activity.code,
          description: activity.descri,
          montoBase: activity.montoBase,
          alicuota: activity.alicuota,
          impuesto: calculateTax(activity.montoBase, activity.alicuota)
        }));

      const submissionData = {
        ...formData,
        activities: activitiesWithAmounts,
        totalDeclared: calculateTotalDeclared()
      };

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      showToast(
        'Declaración enviada',
        'La declaración de actividades económicas ha sido enviada exitosamente',
        'success'
      );

      // Close confirmation modal
      setShowConfirmModal(false);

      // Reset editable form fields
      resetEditableFields();

      console.log('Form data submitted:', submissionData);
      console.log('Activities with amounts:', activitiesWithAmounts);

    } catch (error) {
      console.error('Submission error:', error);
      showToast(
        'Error al enviar',
        'No se pudo enviar la declaración. Intente nuevamente.',
        'error'
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Set page title
  useEffect(() => {
    document.title = 'Taxia | Actividades Económicas';
  }, []);

  return (
    <>
      <Container>
        <Header>
          <Title>Actividades Económicas</Title>
          <Subtitle>Declaración de actividades económicas del contribuyente</Subtitle>
        </Header>

        <StyledForm onSubmit={handleSubmit}>
          {/* 1. Contributor Information Section (Read-only) */}
          <Section>
            <SectionHeader>
              <SectionIcon>
                <UserIcon />
              </SectionIcon>
              <SectionTitle>Información del Contribuyente</SectionTitle>
              <ReadOnlyBadge>Solo lectura</ReadOnlyBadge>
            </SectionHeader>

            <ContributorGrid>
              <InfoField>
                <InfoLabel>Nombre o razón social</InfoLabel>
                <InfoValue>{formData.contributor.nameOrBusinessName}</InfoValue>
              </InfoField>

              <InfoField>
                <InfoLabel>Número de licencia</InfoLabel>
                <InfoValue>{formData.contributor.licenseNumber}</InfoValue>
              </InfoField>

              <InfoField>
                <InfoLabel>RIF</InfoLabel>
                <InfoValue>{formData.contributor.rif}</InfoValue>
              </InfoField>

              <InfoField>
                <InfoLabel>Email</InfoLabel>
                <InfoValue>{formData.contributor.email}</InfoValue>
              </InfoField>

              <InfoField>
                <InfoLabel>Responsable legal</InfoLabel>
                <InfoValue>{formData.contributor.legalResponsible}</InfoValue>
              </InfoField>

              <InfoField>
                <InfoLabel>Teléfono local</InfoLabel>
                <InfoValue>{formData.contributor.localPhone}</InfoValue>
              </InfoField>

              <InfoField>
                <InfoLabel>Celular</InfoLabel>
                <InfoValue>{formData.contributor.cellPhone}</InfoValue>
              </InfoField>

              <InfoField $fullWidth>
                <InfoLabel>Dirección</InfoLabel>
                <InfoValue>{formData.contributor.address}</InfoValue>
              </InfoField>
            </ContributorGrid>
          </Section>

          {/* 2. Declaration Section */}
          <Section>
            <SectionHeader>
              <SectionIcon>
                <DocumentTextIcon />
              </SectionIcon>
              <SectionTitle>Declaración</SectionTitle>
            </SectionHeader>

            <FormGrid>
              <FormField name="declarationType">
                <FormLabel>Tipo de declaración *</FormLabel>
                <Select
                  value={formData.declaration.type}
                  onChange={(e) => handleDeclarationChange('type', e.target.value as 'definitiva' | 'sustitutiva' | 'anticipada')}
                  $hasError={!!errors.declarationType}
                >
                  {DECLARATION_TYPES.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </Select>
                {errors.declarationType && <ErrorMessage>{errors.declarationType}</ErrorMessage>}
              </FormField>

              <FormField name="declarationYear">
                <FormLabel>Año *</FormLabel>
                <Select
                  value={formData.declaration.year}
                  onChange={(e) => handleDeclarationChange('year', parseInt(e.target.value))}
                  $hasError={!!errors.declarationYear}
                >
                  {availableYears.map(year => (
                    <option key={year.value} value={year.value}>
                      {year.label}
                    </option>
                  ))}
                </Select>
                {errors.declarationYear && <ErrorMessage>{errors.declarationYear}</ErrorMessage>}
              </FormField>

              <FormField name="declarationMonth">
                <FormLabel>Mes *</FormLabel>
                <Select
                  value={formData.declaration.month}
                  onChange={(e) => handleDeclarationChange('month', parseInt(e.target.value))}
                  $hasError={!!errors.declarationMonth}
                >
                  {MONTHS.map(month => (
                    <option key={month.value} value={month.value}>
                      {month.label}
                    </option>
                  ))}
                </Select>
                {errors.declarationMonth && <ErrorMessage>{errors.declarationMonth}</ErrorMessage>}
              </FormField>
            </FormGrid>
          </Section>

          {/* 3. Location and Economic Activity Section */}
          <Section>
            <SectionHeader>
              <SectionIcon>
                <BriefcaseIcon />
              </SectionIcon>
              <SectionTitle>Ubicación y Actividad Económica</SectionTitle>
            </SectionHeader>

            <FormGrid>
              <FormField name="location">
                <FormLabel>Ubicación del contribuyente *</FormLabel>
                <LocationButton
                  type="button"
                  onClick={() => setIsLocationModalOpen(true)}
                  $hasError={!!errors.location}
                  $hasValue={!!formData.location}
                >
                  <MapPinIcon />
                  {formData.location
                    ? `Lat: ${formData.location.latitude.toFixed(6)}, Lng: ${formData.location.longitude.toFixed(6)}`
                    : 'Seleccionar ubicación en el mapa'
                  }
                </LocationButton>
                {errors.location && <ErrorMessage>{errors.location}</ErrorMessage>}
              </FormField>

              <FormField name="economicActivities" $fullWidth>
                <FormLabel>Actividades económicas registradas</FormLabel>
                <ActivitiesTable>
                  <TableHeader>
                    <TableRow>
                      <TableHeaderCell>Actividades grabadas</TableHeaderCell>
                      <TableHeaderCell>Código</TableHeaderCell>
                      <TableHeaderCell>Monto base</TableHeaderCell>
                      <TableHeaderCell>Alícuota</TableHeaderCell>
                      <TableHeaderCell>Impuesto</TableHeaderCell>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {activities.map((activity) => (
                      <TableRow key={activity.number}>
                        <TableCell>{activity.descri}</TableCell>
                        <TableCell>{activity.code}</TableCell>
                        <TableCell>
                          <MontoInput
                            type="text"
                            value={editingInput === activity.number
                              ? formatForEditing(activity.montoBase)
                              : (activity.montoBase > 0 ? formatCurrency(activity.montoBase) : '')
                            }
                            onChange={(e) => handleMontoBaseChange(activity.number, e.target.value)}
                            onFocus={() => setEditingInput(activity.number)}
                            onBlur={() => setEditingInput(null)}
                            onKeyDown={handleKeyPress}
                            placeholder="0.00"
                            inputMode="decimal"
                          />
                        </TableCell>
                        <TableCell>{activity.alicuota}</TableCell>
                        <TableCellAmount>
                          <TaxAmount>
                            {calculateTax(activity.montoBase, activity.alicuota).toFixed(2)}
                          </TaxAmount>
                        </TableCellAmount>
                      </TableRow>
                    ))}
                  </TableBody>
                </ActivitiesTable>

                <TotalSection>
                  <TotalLabel>Total declarado:</TotalLabel>
                  <TotalAmount>
                    {formatCurrency(calculateTotalDeclared())}
                  </TotalAmount>
                </TotalSection>
                {errors.activities && <ErrorMessage>{errors.activities}</ErrorMessage>}
              </FormField>
            </FormGrid>
          </Section>

          {/* 4. Documents Section */}
          <Section>
            <SectionHeader>
              <SectionIcon>
                <PaperClipIcon />
              </SectionIcon>
              <SectionTitle>Documentos Adjuntos</SectionTitle>
            </SectionHeader>

            <DocumentsInfo>
              <InfoIcon>
                <DocumentTextIcon />
              </InfoIcon>
              <InfoText>
                De ser contribuyente especial, deberá cargar las dos declaraciones del IVA correspondientes a cada quincena.
              </InfoText>
            </DocumentsInfo>

            <DocumentsGrid>
              <FormField name="ivaDeclaration">
                <FormLabel>Declaración de IVA del periodo *</FormLabel>
                <FileUpload
                  onFileSelect={handleFileSelect('ivaDeclaration')}
                  selectedFile={formData.ivaDeclaration}
                  accept=".pdf"
                />
                <FormDescription>
                  Archivo PDF de la declaración de IVA correspondiente al periodo declarado (máx. 10MB)
                </FormDescription>
                {errors.ivaDeclaration && <ErrorMessage>{errors.ivaDeclaration}</ErrorMessage>}
              </FormField>

              <FormField name="ivaRelationDeclaration">
                <FormLabel>Declaración de IVA relación de ingreso *</FormLabel>
                <FileUpload
                  onFileSelect={handleFileSelect('ivaRelationDeclaration')}
                  selectedFile={formData.ivaRelationDeclaration}
                  accept=".pdf"
                  />
                <FormDescription>
                  Archivo PDF de la declaración de IVA relación de ingreso (máx. 10MB)
                </FormDescription>
                {errors.ivaRelationDeclaration && <ErrorMessage>{errors.ivaRelationDeclaration}</ErrorMessage>}
              </FormField>
            </DocumentsGrid>
          </Section>

          {/* Submit Button */}
          <SubmitSection>
            <SubmitButton type="submit" disabled={isLoading}>
              <CalendarIcon />
              Enviar Declaración
            </SubmitButton>
          </SubmitSection>
        </StyledForm>
      </Container>

      {/* Modals */}
      <EconomicActivityModal
        isOpen={isActivityModalOpen}
        onClose={() => setIsActivityModalOpen(false)}
        onSelect={handleActivitySelect}
        selectedActivity={formData.selectedActivity}
      />

      <LocationSelector
        isOpen={isLocationModalOpen}
        onClose={() => setIsLocationModalOpen(false)}
        onSelect={handleLocationSelect}
        currentLocation={formData.location}
      />

      {/* Confirmation Modal */}
      <EconomicActivityConfirmationModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleConfirmedSubmit}
        data={formData}
        activities={activities
          .filter(activity => activity.montoBase > 0)
          .map(activity => ({
            code: activity.code,
            description: activity.descri,
            montoBase: activity.montoBase,
            alicuota: activity.alicuota,
            impuesto: calculateTax(activity.montoBase, activity.alicuota)
          }))
        }
        totalDeclared={calculateTotalDeclared()}
        isLoading={isLoading}
      />

      {/* Loading Overlay */}
      <LoadingOverlay
        isVisible={isLoading}
        message="Enviando declaración..."
      />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${props => props.theme.space[6]};
`;

const Header = styled.div`
  margin-bottom: ${props => props.theme.space[8]};
  text-align: center;
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.space[2]};
  transition: color 0.3s ease;
`;

const Subtitle = styled.p`
  font-size: ${props => props.theme.fontSizes.lg};
  color: ${props => props.theme.colors.textLight};
  margin: 0;
  transition: color 0.3s ease;
`;

const StyledForm = styled(Form.Root)`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[8]};
`;

const Section = styled.div`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  padding: ${props => props.theme.space[6]};
  box-shadow: ${props => props.theme.shadows.sm};
  border: 1px solid ${props => props.theme.colors.borderLight};
  transition: all 0.3s ease;
`;

const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[3]};
  margin-bottom: ${props => props.theme.space[6]};
  padding-bottom: ${props => props.theme.space[4]};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const SectionIcon = styled.div`
  width: 24px;
  height: 24px;
  color: ${props => props.theme.colors.primary};

  svg {
    width: 100%;
    height: 100%;
  }
`;

const SectionTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
  flex: 1;
  transition: color 0.3s ease;
`;

const ReadOnlyBadge = styled.span`
  padding: ${props => props.theme.space[1]} ${props => props.theme.space[2]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  color: ${props => props.theme.colors.textLight};
  font-size: ${props => props.theme.fontSizes.xs};
  font-weight: ${props => props.theme.fontWeights.medium};
  border-radius: ${props => props.theme.radii.md};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ContributorGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.space[4]};

  @media (min-width: 640px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
`;

const InfoField = styled.div<{ $fullWidth?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[1]};

  ${props => props.$fullWidth && `
    @media (min-width: 640px) {
      grid-column: 1 / -1;
    }
  `}
`;

const InfoLabel = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.textLight};
`;

const InfoValue = styled.div`
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.text};
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[3]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  border: 1px solid ${props => props.theme.colors.borderLight};
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.space[4]};

  @media (min-width: 640px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
`;

const FormField = styled(Form.Field) <{ $fullWidth?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[2]};

  ${props => props.$fullWidth && `
    @media (min-width: 640px) {
      grid-column: 1 / -1;
    }
  `}
`;

const FormLabel = styled(Form.Label)`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const Select = styled.select<{ $hasError?: boolean }>`
  width: 100%;
  height: 42px;
  padding: 0 ${props => props.theme.space[3]};
  border: 1px solid ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  background-color: ${props => props.theme.colors.white};
  color: ${props => props.theme.colors.text};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => `${props.theme.colors.primary}20`};
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundMuted};
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

const LocationButton = styled.button<{ $hasError?: boolean; $hasValue?: boolean }>`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  width: 100%;
  height: 42px;
  padding: 0 ${props => props.theme.space[3]};
  border: 1px solid ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  background-color: ${props => props.theme.colors.white};
  color: ${props => props.$hasValue ? props.theme.colors.text : props.theme.colors.textLight};
  font-size: ${props => props.theme.fontSizes.md};
  text-align: left;
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 16px;
    height: 16px;
    color: ${props => props.theme.colors.primary};
  }

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    background-color: ${props => props.theme.colors.backgroundMuted};
  }

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => `${props.theme.colors.primary}20`};
  }
`;

const ActivityButton = styled.button<{ $hasError?: boolean; $hasValue?: boolean }>`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  width: 100%;
  min-height: 42px;
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[3]};
  border: 1px solid ${props => props.$hasError ? props.theme.colors.danger : props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  background-color: ${props => props.theme.colors.white};
  color: ${props => props.$hasValue ? props.theme.colors.text : props.theme.colors.textLight};
  font-size: ${props => props.theme.fontSizes.md};
  text-align: left;
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 16px;
    height: 16px;
    color: ${props => props.theme.colors.primary};
    flex-shrink: 0;
  }

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    background-color: ${props => props.theme.colors.backgroundMuted};
  }

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => `${props.theme.colors.primary}20`};
  }
`;

const ActivityDetails = styled.div`
  display: flex;
  gap: ${props => props.theme.space[4]};
  margin-top: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[3]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  border: 1px solid ${props => props.theme.colors.borderLight};

  @media (max-width: 640px) {
    flex-direction: column;
    gap: ${props => props.theme.space[2]};
  }
`;

const ActivityDetail = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
`;

const DocumentsInfo = styled.div`
  display: flex;
  align-items: flex-start;
  gap: ${props => props.theme.space[3]};
  padding: ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  border: 1px solid ${props => props.theme.colors.borderLight};
  margin-bottom: ${props => props.theme.space[6]};
`;

const InfoIcon = styled.div`
  width: 20px;
  height: 20px;
  color: ${props => props.theme.colors.primary};
  flex-shrink: 0;
  margin-top: 2px;

  svg {
    width: 100%;
    height: 100%;
  }
`;

const InfoText = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
  margin: 0;
  line-height: 1.5;
`;

const DocumentsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.space[6]};

  @media (min-width: 768px) {
    grid-template-columns: 1fr 1fr;
  }
`;

const FormDescription = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  margin: ${props => props.theme.space[1]} 0 0;
  transition: color 0.3s ease;
`;

const ErrorMessage = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.danger};
  margin-top: ${props => props.theme.space[1]};
  transition: color 0.3s ease;
`;

const SubmitSection = styled.div`
  display: flex;
  justify-content: center;
  padding-top: ${props => props.theme.space[4]};
`;

const SubmitButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[4]} ${props => props.theme.space[8]};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;

  svg {
    width: 20px;
    height: 20px;
  }

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.primaryHover};
    transform: translateY(-1px);
    box-shadow: ${props => props.theme.shadows.md};
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

// Table styles
const ActivitiesTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.md};
  overflow: hidden;
  box-shadow: ${props => props.theme.shadows.sm};
`;

const TableHeader = styled.thead`
  background-color: ${props => props.theme.colors.backgroundMuted};
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  &:not(:last-child) {
    border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  }

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted};
  }
`;

const TableHeaderCell = styled.th`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[4]};
  text-align: left;
  font-weight: ${props => props.theme.fontWeights.semibold};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const TableCell = styled.td`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[4]};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
  vertical-align: middle;
`;

const TableCellAmount = styled.td`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[4]};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
  vertical-align: middle;
  text-align: right;
`;

const MontoInput = styled.input`
  width: 100%;
  padding: ${props => props.theme.space[2]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.sm};
  font-size: ${props => props.theme.fontSizes.sm};
  background-color: ${props => props.theme.colors.white};
  transition: ${props => props.theme.transitions.default};
  color: ${props => props.theme.colors.text};
  text-align: right;
  font-family: 'Courier New', monospace;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;
  }

  &:hover {
    border-color: ${props => props.theme.colors.border};
  }

  /* Allow decimal input */
  &[inputmode="decimal"] {
    -webkit-appearance: none;
    -moz-appearance: textfield;
  }

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
`;

const TaxAmount = styled.span`
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.primary};
`;

const TotalSection = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: ${props => props.theme.space[3]};
  margin-top: ${props => props.theme.space[4]};
  padding: ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  border: 1px solid ${props => props.theme.colors.borderLight};
`;

const TotalLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
`;

const TotalAmount = styled.span`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.primary};
  min-width: 120px;
  text-align: right;
`;

export default EconomicActivities;
