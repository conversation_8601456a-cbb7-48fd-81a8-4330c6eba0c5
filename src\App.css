:root {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --secondary-color: #6b7280;
  --secondary-hover: #4b5563;
  --background-color: #f9fafb;
  --card-background: #ffffff;
  --text-color: #111827;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header Styles */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--card-background);
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Button Styles */
.button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  border: none;
  outline: none;
}

.button.primary {
  background-color: var(--primary-color);
  color: white;
}

.button.primary:hover {
  background-color: var(--primary-hover);
}

.button.secondary {
  background-color: white;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.button.secondary:hover {
  background-color: var(--background-color);
}

.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  color: var(--text-color);
}

.icon-button:hover {
  background-color: var(--background-color);
}

/* Main Content Styles */
.app-main {
  flex: 1;
  padding: 2rem;
}

/* Tabs Styles */
.tabs-root {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.tabs-list {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
}

.tabs-trigger {
  padding: 0.75rem 1rem;
  background-color: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s, border-color 0.2s;
}

.tabs-trigger:hover {
  color: var(--text-color);
}

.tabs-trigger[data-state="active"] {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tabs-content {
  padding: 1rem 0;
}

.tabs-content h2 {
  margin-bottom: 1rem;
}

/* Dialog Styles */
.dialog-overlay {
  background-color: rgba(0, 0, 0, 0.4);
  position: fixed;
  inset: 0;
  animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
}

.dialog-content {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: var(--shadow);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 500px;
  max-height: 85vh;
  padding: 1.5rem;
  animation: contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
}

.dialog-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.dialog-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.dialog-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.form-field {
  margin-bottom: 1rem;
}

.form-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-field input,
.form-field select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 1rem;
}

/* Dropdown Menu Styles */
.dropdown-content {
  min-width: 180px;
  background-color: white;
  border-radius: 0.375rem;
  padding: 0.5rem;
  box-shadow: var(--shadow);
  animation: scaleIn 200ms ease;
}

.dropdown-item {
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  cursor: pointer;
  outline: none;
  user-select: none;
}

.dropdown-item:hover {
  background-color: var(--background-color);
}

.dropdown-separator {
  height: 1px;
  background-color: var(--border-color);
  margin: 0.5rem 0;
}

/* Avatar Styles */
.avatar-root {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  overflow: hidden;
  user-select: none;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  background-color: var(--background-color);
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--secondary-color);
  color: white;
}

/* Toast Styles */
.toast-viewport {
  position: fixed;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  gap: 0.75rem;
  width: 390px;
  max-width: 100vw;
  margin: 0;
  list-style: none;
  z-index: 2147483647;
  outline: none;
}

.toast-root {
  background-color: white;
  border-radius: 0.375rem;
  box-shadow: var(--shadow);
  padding: 1rem;
  display: grid;
  grid-template-areas: 'title action' 'description action';
  grid-template-columns: auto max-content;
  column-gap: 0.75rem;
  align-items: center;
}

.toast-title {
  grid-area: title;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.toast-description {
  grid-area: description;
  color: var(--text-secondary);
}

.toast-close {
  grid-area: action;
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
}

/* Animations */
@keyframes overlayShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes contentShow {
  from {
    opacity: 0;
    transform: translate(-50%, -48%) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
