// src/pages/Login.tsx
import React, { useState } from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import * as Form from '@radix-ui/react-form';
import { EnvelopeClosedIcon, LockClosedIcon, ExclamationTriangleIcon } from '@radix-ui/react-icons';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Simple validation
    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    // For demo purposes, accept any login
    // In a real app, you would validate credentials with your backend
    navigate('/');
  };

  return (
    <LoginContainer>
      <LoginCard>
        <LoginHeader>
          <Logo>Admin Panel</Logo>
          <LoginTitle>Sign in to your account</LoginTitle>
          <LoginSubtitle>Enter your credentials to access your account</LoginSubtitle>
        </LoginHeader>

        {error && (
          <ErrorMessage>
            <ExclamationTriangleIcon />
            <span>{error}</span>
          </ErrorMessage>
        )}

        <StyledForm onSubmit={handleSubmit}>
          <FormField name="email">
            <FormLabel>Email</FormLabel>
            <FormControl>
              <InputIcon>
                <EnvelopeClosedIcon />
              </InputIcon>
              <Input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </FormControl>
            <FormMessage match="valueMissing">
              Please enter your email
            </FormMessage>
            <FormMessage match="typeMismatch">
              Please enter a valid email
            </FormMessage>
          </FormField>

          <FormField name="password">
            <FormLabel>Password</FormLabel>
            <FormControl>
              <InputIcon>
                <LockClosedIcon />
              </InputIcon>
              <Input
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </FormControl>
            <FormMessage match="valueMissing">
              Please enter your password
            </FormMessage>
          </FormField>

          <FormOptions>
            <RememberMe>
              <Checkbox type="checkbox" id="remember" />
              <label htmlFor="remember">Remember me</label>
            </RememberMe>
            <ForgotPassword>Forgot password?</ForgotPassword>
          </FormOptions>

          <SubmitButton type="submit">
            Sign In
          </SubmitButton>
        </StyledForm>
      </LoginCard>
    </LoginContainer>
  );
};

// Styled Components
const LoginContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: ${props => props.theme.colors.background};
  padding: ${props => props.theme.space[4]};
  transition: background-color 0.3s ease;
`;

const LoginCard = styled.div`
  width: 100%;
  max-width: 420px;
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  box-shadow: ${props => props.theme.shadows.lg};
  padding: ${props => props.theme.space[6]};
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
`;

const LoginHeader = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.space[6]};
`;

const Logo = styled.div`
  font-size: ${props => props.theme.fontSizes['2xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.space[4]};
  transition: color 0.3s ease;
`;

const LoginTitle = styled.h1`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.space[2]};
  transition: color 0.3s ease;
`;

const LoginSubtitle = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const ErrorMessage = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[3]};
  background-color: ${props => `${props.theme.colors.danger}20`};
  color: ${props => props.theme.colors.danger};
  border-radius: ${props => props.theme.radii.md};
  margin-bottom: ${props => props.theme.space[4]};
  transition: background-color 0.3s ease, color 0.3s ease;

  svg {
    flex-shrink: 0;
  }
`;

const StyledForm = styled(Form.Root)`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[4]};
`;

const FormField = styled(Form.Field)`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[2]};
`;

const FormLabel = styled(Form.Label)`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const FormControl = styled.div`
  position: relative;
`;

const InputIcon = styled.div`
  position: absolute;
  top: 50%;
  left: ${props => props.theme.space[3]};
  transform: translateY(-50%);
  color: ${props => props.theme.colors.textLight};
  pointer-events: none;
  transition: color 0.3s ease;
`;

const Input = styled.input`
  width: 100%;
  height: 42px;
  padding: 0 ${props => props.theme.space[3]} 0 ${props => props.theme.space[8]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  transition: ${props => props.theme.transitions.default};
  background-color: ${props => props.theme.themeMode === 'dark' ? props.theme.colors.backgroundMuted : 'transparent'};
  color: ${props => props.theme.colors.text};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => `${props.theme.colors.primary}20`};
  }

  &::placeholder {
    color: ${props => props.theme.colors.textLight};
  }
`;

const FormMessage = styled(Form.Message)`
  font-size: ${props => props.theme.fontSizes.xs};
  color: ${props => props.theme.colors.danger};
  margin-top: ${props => props.theme.space[1]};
  transition: color 0.3s ease;
`;

const FormOptions = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: ${props => props.theme.space[2]};
`;

const RememberMe = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: ${props => props.theme.colors.primary};
`;

const ForgotPassword = styled.a`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.primary};
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    text-decoration: underline;
  }
`;

const SubmitButton = styled.button`
  width: 100%;
  height: 42px;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  border-radius: ${props => props.theme.radii.md};
  transition: ${props => props.theme.transitions.default};
  margin-top: ${props => props.theme.space[4]};

  &:hover {
    background-color: ${props => props.theme.colors.primaryHover};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${props => `${props.theme.colors.primary}40`};
  }
`;

export default Login;
