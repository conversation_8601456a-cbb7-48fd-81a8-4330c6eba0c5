// src/components/SidebarMenu.tsx
import React from 'react';
import styled from 'styled-components';
import { NavLink } from 'react-router-dom';

// HeroIcons
import {
  HomeIcon,
  FolderIcon,
  ChartBarIcon,
  TruckIcon,
  DocumentTextIcon,
  ReceiptPercentIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  DocumentIcon,
  CreditCardIcon,
  BookOpenIcon,
  BriefcaseIcon,
  HomeModernIcon,
  TrashIcon,
  FireIcon,
  BeakerIcon,
  Cog6ToothIcon,
  SpeakerWaveIcon as MegaphoneIcon,
  TicketIcon
} from '@heroicons/react/24/outline';

interface SidebarMenuProps {
  isCollapsed: boolean;
}

const SidebarMenu: React.FC<SidebarMenuProps> = ({ isCollapsed }) => {
  return (
    <NavContainer>
      {/* Inicio - Elemento simple */}
      <NavItem $isCollapsed={isCollapsed}>
        <StyledNavLink to="/" end>
          <IconWrapper>
            <HomeIcon />
          </IconWrapper>
          {!isCollapsed && <span>Inicio</span>}
        </StyledNavLink>
      </NavItem>

      {/* Registros - Grupo de menú */}
      {!isCollapsed ? (
        <MenuGroup>
          <MenuGroupTitle>Registros</MenuGroupTitle>
          <MenuGroupContent>
            {/* Actividades económicas */}
            <SubNavItem>
              <StyledNavLink to="/registros/actividades-economicas">
                <IconWrapper>
                  <ChartBarIcon />
                </IconWrapper>
                <span>Actividades económicas</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Inmuebles Urbanos */}
            <SubNavItem>
              <StyledNavLink to="/registros/inmuebles-urbanos">
                <IconWrapper>
                  <HomeModernIcon />
                </IconWrapper>
                <span>Inmuebles Urbanos</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Vehículos */}
            <SubNavItem>
              <StyledNavLink to="/registros/vehiculos">
                <IconWrapper>
                  <TruckIcon />
                </IconWrapper>
                <span>Vehículos</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Publicidad y Propaganda */}
            <SubNavItem>
              <StyledNavLink to="/registros/publicidad-propaganda">
                <IconWrapper>
                  <MegaphoneIcon />
                </IconWrapper>
                <span>Publicidad y Propaganda</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Espectáculos públicos */}
            <SubNavItem>
              <StyledNavLink to="/registros/espectaculos-publicos">
                <IconWrapper>
                  <TicketIcon />
                </IconWrapper>
                <span>Espectáculos públicos</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Apuestas lícitas */}
            <SubNavItem>
              <StyledNavLink to="/registros/apuestas-licitas">
                <IconWrapper>
                  <CurrencyDollarIcon />
                </IconWrapper>
                <span>Apuestas lícitas</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Tasas */}
            <SubNavItem>
              <StyledNavLink to="/registros/tasas">
                <IconWrapper>
                  <ReceiptPercentIcon />
                </IconWrapper>
                <span>Tasas</span>
              </StyledNavLink>
            </SubNavItem>
          </MenuGroupContent>
        </MenuGroup>
      ) : (
        <NavItem $isCollapsed={isCollapsed}>
          <StyledNavLink to="/registros">
            <IconWrapper>
              <FolderIcon />
            </IconWrapper>
          </StyledNavLink>
        </NavItem>
      )}

      {/* Procesos tributarios - Grupo de menú */}
      {!isCollapsed ? (
        <MenuGroup>
          <MenuGroupTitle>Procesos tributarios</MenuGroupTitle>
          <MenuGroupContent>
            {/* Rebajas de serv. municipal */}
            <SubNavItem>
              <StyledNavLink to="/procesos-tributarios/rebajas-servicios">
                <IconWrapper>
                  <ReceiptPercentIcon />
                </IconWrapper>
                <span>Rebajas de serv. municipal</span>
              </StyledNavLink>
            </SubNavItem>
          </MenuGroupContent>
        </MenuGroup>
      ) : (
        <NavItem $isCollapsed={isCollapsed}>
          <StyledNavLink to="/procesos-tributarios">
            <IconWrapper>
              <DocumentTextIcon />
            </IconWrapper>
          </StyledNavLink>
        </NavItem>
      )}

      {/* Agentes de retenciones - Grupo de menú */}
      {!isCollapsed ? (
        <MenuGroup>
          <MenuGroupTitle>Agentes de retenciones</MenuGroupTitle>
          <MenuGroupContent>
            {/* Retenciones IAE */}
            <SubNavItem>
              <StyledNavLink to="/agentes-retenciones/retenciones-iae">
                <IconWrapper>
                  <CurrencyDollarIcon />
                </IconWrapper>
                <span>Retenciones IAE</span>
              </StyledNavLink>
            </SubNavItem>
          </MenuGroupContent>
        </MenuGroup>
      ) : (
        <NavItem $isCollapsed={isCollapsed}>
          <StyledNavLink to="/agentes-retenciones">
            <IconWrapper>
              <BuildingOfficeIcon />
            </IconWrapper>
          </StyledNavLink>
        </NavItem>
      )}

      {/* Consultas - Grupo de menú */}
      {!isCollapsed ? (
        <MenuGroup>
          <MenuGroupTitle>Consultas</MenuGroupTitle>
          <MenuGroupContent>
            {/* Estado de cuenta */}
            <SubNavItem>
              <StyledNavLink to="/consultas/estado-cuenta">
                <IconWrapper>
                  <DocumentIcon />
                </IconWrapper>
                <span>Estado de cuenta</span>
              </StyledNavLink>
            </SubNavItem>
          </MenuGroupContent>
        </MenuGroup>
      ) : (
        <NavItem $isCollapsed={isCollapsed}>
          <StyledNavLink to="/consultas">
            <IconWrapper>
              <MagnifyingGlassIcon />
            </IconWrapper>
          </StyledNavLink>
        </NavItem>
      )}

      {/* Pagos - Elemento simple */}
      <NavItem $isCollapsed={isCollapsed}>
        <StyledNavLink to="/pagos">
          <IconWrapper>
            <CreditCardIcon />
          </IconWrapper>
          {!isCollapsed && <span>Pagos</span>}
        </StyledNavLink>
      </NavItem>

      {/* Base legal - Grupo de menú */}
      {!isCollapsed ? (
        <MenuGroup>
          <MenuGroupTitle>Base legal</MenuGroupTitle>
          <MenuGroupContent>
            {/* Actividades económicas */}
            <SubNavItem>
              <StyledNavLink to="/base-legal/actividades-economicas">
                <IconWrapper>
                  <BriefcaseIcon />
                </IconWrapper>
                <span>Actividades económicas</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Inmuebles urbanos */}
            <SubNavItem>
              <StyledNavLink to="/base-legal/inmuebles-urbanos">
                <IconWrapper>
                  <HomeModernIcon />
                </IconWrapper>
                <span>Inmuebles urbanos</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Servicio de aseo urbano */}
            <SubNavItem>
              <StyledNavLink to="/base-legal/aseo-urbano">
                <IconWrapper>
                  <TrashIcon />
                </IconWrapper>
                <span>Servicio de aseo urbano</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Servicio de gas doméstico */}
            <SubNavItem>
              <StyledNavLink to="/base-legal/gas-domestico">
                <IconWrapper>
                  <FireIcon />
                </IconWrapper>
                <span>Servicio de gas doméstico</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Vehículos */}
            <SubNavItem>
              <StyledNavLink to="/base-legal/vehiculos">
                <IconWrapper>
                  <TruckIcon />
                </IconWrapper>
                <span>Vehículos</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Licores */}
            <SubNavItem>
              <StyledNavLink to="/base-legal/licores">
                <IconWrapper>
                  <BeakerIcon />
                </IconWrapper>
                <span>Licores</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Publicidad y Propaganda */}
            <SubNavItem>
              <StyledNavLink to="/base-legal/publicidad-propaganda">
                <IconWrapper>
                  <MegaphoneIcon />
                </IconWrapper>
                <span>Publicidad y Propaganda</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Espectáculos públicos */}
            <SubNavItem>
              <StyledNavLink to="/base-legal/espectaculos-publicos">
                <IconWrapper>
                  <TicketIcon />
                </IconWrapper>
                <span>Espectáculos públicos</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Apuestas Lícitas */}
            <SubNavItem>
              <StyledNavLink to="/base-legal/apuestas-licitas">
                <IconWrapper>
                  <CurrencyDollarIcon />
                </IconWrapper>
                <span>Apuestas Lícitas</span>
              </StyledNavLink>
            </SubNavItem>

            {/* Tasas Administrativas */}
            <SubNavItem>
              <StyledNavLink to="/base-legal/tasas-administrativas">
                <IconWrapper>
                  <ReceiptPercentIcon />
                </IconWrapper>
                <span>Tasas Administrativas</span>
              </StyledNavLink>
            </SubNavItem>
          </MenuGroupContent>
        </MenuGroup>
      ) : (
        <NavItem $isCollapsed={isCollapsed}>
          <StyledNavLink to="/base-legal">
            <IconWrapper>
              <BookOpenIcon />
            </IconWrapper>
          </StyledNavLink>
        </NavItem>
      )}

      {/* Autogestión de servicios municipales - Elemento simple */}
      <NavItem $isCollapsed={isCollapsed}>
        <StyledNavLink to="/autogestion">
          <IconWrapper>
            <Cog6ToothIcon />
          </IconWrapper>
          {!isCollapsed && <span>Autogestión de serv. Municipales</span>}
        </StyledNavLink>
      </NavItem>
    </NavContainer>
  );
};

// Styled Components
const NavContainer = styled.nav`
  flex: 1;
  padding: ${props => props.theme.space[3]} 0;
  overflow-y: auto;
`;

const NavItem = styled.div<{ $isCollapsed: boolean }>`
  margin-bottom: ${props => props.theme.space[1]};
  padding: 0 ${props => props.$isCollapsed ? props.theme.space[2] : props.theme.space[3]};
`;

const IconWrapper = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: ${props => props.theme.space[3]};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;

  & > svg {
    width: 16px;
    height: 16px;
  }
`;

const StyledNavLink = styled(NavLink)`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.space[1]} ${props => props.theme.space[3]};
  border-radius: ${props => props.theme.radii.md};
  color: ${props => props.theme.colors.textLight};
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.normal};
  transition: ${props => props.theme.transitions.default};
  text-decoration: none;
  line-height: 1.5;
  position: relative;
  margin-bottom: 2px;

  &:hover {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;
    background-color: ${props =>
      props.theme.themeMode === 'light'
        ? 'rgba(62, 99, 221, 0.06)'
        : 'rgba(91, 124, 250, 0.15)'
    };

    ${IconWrapper} {
      color: ${props => props.theme.colors.primary};
    }
  }

  &.active {
    color: ${props => props.theme.colors.primary};
    font-weight: ${props => props.theme.fontWeights.medium};
    background-color: ${props =>
      props.theme.themeMode === 'light'
        ? 'rgba(62, 99, 221, 0.1)'
        : 'rgba(91, 124, 250, 0.2)'
    };

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: ${props => props.theme.colors.primary};
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;
    }

    ${IconWrapper} {
      color: ${props => props.theme.colors.primary};
    }
  }
`;

const SubNavItem = styled.div`
  margin-bottom: ${props => props.theme.space[1]};

  ${StyledNavLink} {
    padding-left: ${props => props.theme.space[4]};
    font-size: ${props => props.theme.fontSizes.sm};
  }
`;

// Componentes para grupos de menú
const MenuGroup = styled.div`
  margin-bottom: ${props => props.theme.space[1]};
  padding: 0 ${props => props.theme.space[2]};
`;

const MenuGroupTitle = styled.div`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[3]} ${props => props.theme.space[1]};
  color: ${props => props.theme.colors.text};
  font-size: ${props => props.theme.fontSizes.xs};
  font-weight: ${props => props.theme.fontWeights.bold};
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: color 0.3s ease;
`;

const MenuGroupContent = styled.div`
  padding-left: ${props => props.theme.space[1]};
  margin-bottom: ${props => props.theme.space[4]};
`;



export default SidebarMenu;
