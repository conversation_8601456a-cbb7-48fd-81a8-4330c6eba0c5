// src/data/states.ts
export interface State {
  id: string;
  name: string;
}

export const states: State[] = [
  { id: 'amazonas', name: 'Amazonas' },
  { id: 'anzoategu<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { id: 'apure', name: '<PERSON>pur<PERSON>' },
  { id: 'aragua', name: 'Aragua' },
  { id: 'barinas', name: '<PERSON><PERSON>' },
  { id: 'bolivar', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 'carabobo', name: 'Carabo<PERSON>' },
  { id: 'cojedes', name: '<PERSON><PERSON><PERSON>' },
  { id: 'delta_amacuro', name: 'Delta Amacuro' },
  { id: 'distrito_capital', name: 'Distrito Capital' },
  { id: 'falcon', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 'guarico', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { id: 'lara', name: '<PERSON>' },
  { id: 'merida', name: '<PERSON><PERSON><PERSON>' },
  { id: 'miranda', name: '<PERSON>' },
  { id: 'monaga<PERSON>', name: '<PERSON><PERSON>' },
  { id: 'nueva_esparta', name: 'Nueva Esparta' },
  { id: 'portuguesa', name: 'Portuguesa' },
  { id: 'sucre', name: '<PERSON>cre' },
  { id: 'tachira', name: 'T<PERSON>chira' },
  { id: 'trujillo', name: 'Trujillo' },
  { id: 'vargas', name: '<PERSON>' },
  { id: 'yaracuy', name: 'Yaracuy' },
  { id: 'zulia', name: 'Zulia' }
];
