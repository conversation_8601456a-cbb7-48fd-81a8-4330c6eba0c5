// src/styles/theme.ts
export const theme = {
  colors: {
    // Radix UI colors - Indigo palette
    primary: '#3E63DD',       // Indigo9 from Radix Colors
    primaryHover: '#3A5CCC',  // Indigo10 from Radix Colors
    primaryDark: '#3A5CCC',   // Indigo10 from Radix Colors
    primaryLight: '#E0E8FF',  // Indigo3 from Radix Colors
    secondary: '#808080',     // Gray9 from Radix Colors
    secondaryHover: '#6E6E6E', // Gray10 from Radix Colors
    success: '#30A46C',       // Grass9 from Radix Colors
    danger: '#E5484D',        // Red9 from Radix Colors
    dangerDark: '#DC3D43',    // Red10 from Radix Colors
    warning: '#F76808',       // Amber9 from Radix Colors
    info: '#0091FF',          // Blue9 from Radix Colors

    // Background colors
    background: '#FCFCFC',    // Light background from Radix UI
    backgroundDark: '#161618', // Dark background from Radix UI
    backgroundSidebar: '#F9F9F9', // Sidebar background from Radix UI docs
    backgroundMuted: '#F3F3F3', // Muted background from Radix UI

    // Text colors
    text: '#1A1523',          // Main text color
    textLight: '#6F6E77',     // Secondary text color
    textMuted: '#908E96',     // Muted text color

    // Border colors
    border: '#E6E6E7',        // Border color
    borderLight: '#EDEDEF',   // Light border color

    // Other colors
    white: '#FFFFFF',
    shadow: 'rgba(0, 0, 0, 0.08)',
    focus: 'rgba(62, 99, 221, 0.35)', // Focus ring color (Indigo9)
  },
  fonts: {
    body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
    heading: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
    monospace: "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace",
  },
  fontSizes: {
    // Radix UI font sizes
    xs: '0.75rem',      // 12px
    sm: '0.8125rem',    // 13px - Radix UI uses this for sidebar items
    md: '0.875rem',     // 14px - Radix UI uses this for most text
    lg: '0.9375rem',    // 15px
    xl: '1rem',         // 16px
    '2xl': '1.125rem',  // 18px
    '3xl': '1.25rem',   // 20px
    '4xl': '1.5rem',    // 24px
    '5xl': '1.875rem',  // 30px
    '6xl': '2.25rem',   // 36px
  },
  fontWeights: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
  lineHeights: {
    none: 1,
    tight: 1.25,
    normal: 1.5,
    loose: 2,
    // Radix UI specific line heights
    code: 1.6,    // For code blocks
    heading: 1.2, // For headings
  },
  space: {
    0: '0',
    1: '0.25rem',   // 4px
    2: '0.5rem',    // 8px
    3: '0.75rem',   // 12px
    4: '1rem',      // 16px
    5: '1.25rem',   // 20px
    6: '1.5rem',    // 24px
    7: '1.75rem',   // 28px
    8: '2rem',      // 32px
    9: '2.25rem',   // 36px
    10: '2.5rem',   // 40px
    12: '3rem',     // 48px
    16: '4rem',     // 64px
    20: '5rem',     // 80px
    24: '6rem',     // 96px
    32: '8rem',     // 128px
  },
  sizes: {
    sidebarWidth: '260px',      // Radix UI sidebar width
    sidebarCollapsedWidth: '70px',
    topbarHeight: '65px',       // Radix UI topbar height
    maxWidth: '1200px',
  },
  breakpoints: {
    mobile: '480px',
    tablet: '768px',
    desktop: '1024px',
    wide: '1200px',
  },
  radii: {
    none: '0',
    xs: '0.125rem',  // 2px
    sm: '0.25rem',   // 4px
    md: '0.375rem',  // 6px - Radix UI uses this for most elements
    lg: '0.5rem',    // 8px
    xl: '0.75rem',   // 12px
    '2xl': '1rem',   // 16px
    full: '9999px',
  },
  shadows: {
    // Radix UI shadows
    sm: '0 1px 2px rgba(0, 0, 0, 0.06)',
    md: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08)',
    lg: '0 10px 30px -10px rgba(0, 0, 0, 0.15), 0 5px 15px -5px rgba(0, 0, 0, 0.10)',
    xl: '0 20px 40px -15px rgba(0, 0, 0, 0.2)',
    focus: '0 0 0 2px rgba(62, 99, 221, 0.35)', // Focus ring shadow (Indigo9)
    sidebar: '1px 0 3px rgba(0, 0, 0, 0.05)',    // Sidebar shadow
  },
  transitions: {
    default: 'all 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    fast: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    slow: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    menu: 'transform 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    fade: 'opacity 200ms cubic-bezier(0.4, 0, 0.2, 1)',
  },
  zIndices: {
    hide: -1,
    auto: 'auto',
    base: 0,
    docked: 10,
    dropdown: 1000,
    sticky: 1100,
    banner: 1200,
    overlay: 1300,
    sidebarOverlay: 1350,
    modal: 1400,
    popover: 1500,
    skipLink: 1600,
    toast: 1700,
    tooltip: 1800,
  },
};

export type Theme = typeof theme;
