// src/data/municipalities.ts
export interface Municipality {
  id: string;
  name: string;
  stateId: string;
}

export const municipalities: Municipality[] = [
  // Distrito Capital
  { id: 'dc-libertador', name: 'Libertador', stateId: 'distrito_capital' },
  
  // Miranda
  { id: 'mir-baruta', name: '<PERSON><PERSON>', stateId: 'miranda' },
  { id: 'mir-carrizal', name: 'Carrizal', stateId: 'miranda' },
  { id: 'mir-chacao', name: 'Chacao', stateId: 'miranda' },
  { id: 'mir-el_hatillo', name: 'El Hatillo', stateId: 'miranda' },
  { id: 'mir-sucre', name: 'Su<PERSON>', stateId: 'miranda' },
  { id: 'mir-plaza', name: 'Plaza', stateId: 'miranda' },
  { id: 'mir-zamora', name: 'Zamora', stateId: 'miranda' },
  { id: 'mir-cristo<PERSON>_rojas', name: '<PERSON><PERSON><PERSON><PERSON>', stateId: 'miranda' },
  { id: 'mir-los_salias', name: 'Los Salias', stateId: 'miranda' },
  { id: 'mir-paz_castillo', name: '<PERSON>', stateId: 'miranda' },
  { id: 'mir-pedro_gual', name: 'Pedro Gual', stateId: 'miranda' },
  { id: 'mir-simon_bolivar', name: 'Simón Bolívar', stateId: 'miranda' },
  { id: 'mir-urdaneta', name: 'Urdaneta', stateId: 'miranda' },
  { id: 'mir-acevedo', name: 'Acevedo', stateId: 'miranda' },
  { id: 'mir-andres_bello', name: 'Andrés Bello', stateId: 'miranda' },
  { id: 'mir-brion', name: 'Brión', stateId: 'miranda' },
  { id: 'mir-buroz', name: 'Buroz', stateId: 'miranda' },
  { id: 'mir-guaicaipuro', name: 'Guaicaipuro', stateId: 'miranda' },
  { id: 'mir-independencia', name: 'Independencia', stateId: 'miranda' },
  { id: 'mir-lander', name: 'Lander', stateId: 'miranda' },
  
  // Carabobo
  { id: 'car-valencia', name: 'Valencia', stateId: 'carabobo' },
  { id: 'car-puerto_cabello', name: 'Puerto Cabello', stateId: 'carabobo' },
  { id: 'car-guacara', name: 'Guacara', stateId: 'carabobo' },
  { id: 'car-san_joaquin', name: 'San Joaquín', stateId: 'carabobo' },
  { id: 'car-naguanagua', name: 'Naguanagua', stateId: 'carabobo' },
  { id: 'car-san_diego', name: 'San Diego', stateId: 'carabobo' },
  { id: 'car-los_guayos', name: 'Los Guayos', stateId: 'carabobo' },
  { id: 'car-libertador', name: 'Libertador', stateId: 'carabobo' },
  { id: 'car-carlos_arvelo', name: 'Carlos Arvelo', stateId: 'carabobo' },
  { id: 'car-diego_ibarra', name: 'Diego Ibarra', stateId: 'carabobo' },
  { id: 'car-juan_jose_mora', name: 'Juan José Mora', stateId: 'carabobo' },
  { id: 'car-montalban', name: 'Montalbán', stateId: 'carabobo' },
  { id: 'car-bejuma', name: 'Bejuma', stateId: 'carabobo' },
  { id: 'car-miranda', name: 'Miranda', stateId: 'carabobo' },
  
  // Zulia
  { id: 'zul-maracaibo', name: 'Maracaibo', stateId: 'zulia' },
  { id: 'zul-san_francisco', name: 'San Francisco', stateId: 'zulia' },
  { id: 'zul-cabimas', name: 'Cabimas', stateId: 'zulia' },
  { id: 'zul-ciudad_ojeda', name: 'Ciudad Ojeda', stateId: 'zulia' },
  { id: 'zul-machiques', name: 'Machiques de Perijá', stateId: 'zulia' },
  { id: 'zul-santa_rita', name: 'Santa Rita', stateId: 'zulia' },
  { id: 'zul-almirante_padilla', name: 'Almirante Padilla', stateId: 'zulia' },
  { id: 'zul-baralt', name: 'Baralt', stateId: 'zulia' },
  { id: 'zul-catatumbo', name: 'Catatumbo', stateId: 'zulia' },
  { id: 'zul-colon', name: 'Colón', stateId: 'zulia' },
  { id: 'zul-francisco_javier_pulgar', name: 'Francisco Javier Pulgar', stateId: 'zulia' },
  { id: 'zul-jesus_enrique_lossada', name: 'Jesús Enrique Lossada', stateId: 'zulia' },
  { id: 'zul-jesus_maria_semprun', name: 'Jesús María Semprún', stateId: 'zulia' },
  { id: 'zul-la_canada_de_urdaneta', name: 'La Cañada de Urdaneta', stateId: 'zulia' },
  { id: 'zul-lagunillas', name: 'Lagunillas', stateId: 'zulia' },
  { id: 'zul-mara', name: 'Mara', stateId: 'zulia' },
  { id: 'zul-miranda', name: 'Miranda', stateId: 'zulia' },
  { id: 'zul-rosario_de_perija', name: 'Rosario de Perijá', stateId: 'zulia' },
  { id: 'zul-sucre', name: 'Sucre', stateId: 'zulia' },
  { id: 'zul-valmore_rodriguez', name: 'Valmore Rodríguez', stateId: 'zulia' },
  
  // Aragua
  { id: 'ara-girardot', name: 'Girardot', stateId: 'aragua' },
  { id: 'ara-mario_briceño_iragorry', name: 'Mario Briceño Iragorry', stateId: 'aragua' },
  { id: 'ara-santiago_mariño', name: 'Santiago Mariño', stateId: 'aragua' },
  { id: 'ara-santos_michelena', name: 'Santos Michelena', stateId: 'aragua' },
  { id: 'ara-sucre', name: 'Sucre', stateId: 'aragua' },
  { id: 'ara-jose_angel_lamas', name: 'José Ángel Lamas', stateId: 'aragua' },
  { id: 'ara-jose_felix_ribas', name: 'José Félix Ribas', stateId: 'aragua' },
  { id: 'ara-jose_rafael_revenga', name: 'José Rafael Revenga', stateId: 'aragua' },
  { id: 'ara-libertador', name: 'Libertador', stateId: 'aragua' },
  { id: 'ara-san_casimiro', name: 'San Casimiro', stateId: 'aragua' },
  { id: 'ara-san_sebastian', name: 'San Sebastián', stateId: 'aragua' },
  { id: 'ara-urdaneta', name: 'Urdaneta', stateId: 'aragua' },
  { id: 'ara-zamora', name: 'Zamora', stateId: 'aragua' },
  { id: 'ara-bolivar', name: 'Bolívar', stateId: 'aragua' },
  { id: 'ara-camatagua', name: 'Camatagua', stateId: 'aragua' },
  { id: 'ara-francisco_linares_alcantara', name: 'Francisco Linares Alcántara', stateId: 'aragua' },
  { id: 'ara-ocumare_de_la_costa', name: 'Ocumare de la Costa de Oro', stateId: 'aragua' },
  { id: 'ara-tovar', name: 'Tovar', stateId: 'aragua' },
  
  // Lara
  { id: 'lar-iribarren', name: 'Iribarren', stateId: 'lara' },
  { id: 'lar-palavecino', name: 'Palavecino', stateId: 'lara' },
  { id: 'lar-simon_planas', name: 'Simón Planas', stateId: 'lara' },
  { id: 'lar-torres', name: 'Torres', stateId: 'lara' },
  { id: 'lar-urdaneta', name: 'Urdaneta', stateId: 'lara' },
  { id: 'lar-andres_eloy_blanco', name: 'Andrés Eloy Blanco', stateId: 'lara' },
  { id: 'lar-crespo', name: 'Crespo', stateId: 'lara' },
  { id: 'lar-jimenez', name: 'Jiménez', stateId: 'lara' },
  { id: 'lar-moran', name: 'Morán', stateId: 'lara' },
  
  // Anzoátegui
  { id: 'anz-bolivar', name: 'Bolívar', stateId: 'anzoategui' },
  { id: 'anz-diego_bautista_urbaneja', name: 'Diego Bautista Urbaneja', stateId: 'anzoategui' },
  { id: 'anz-fernando_peñalver', name: 'Fernando de Peñalver', stateId: 'anzoategui' },
  { id: 'anz-francisco_del_carmen_carvajal', name: 'Francisco del Carmen Carvajal', stateId: 'anzoategui' },
  { id: 'anz-francisco_de_miranda', name: 'Francisco de Miranda', stateId: 'anzoategui' },
  { id: 'anz-guanta', name: 'Guanta', stateId: 'anzoategui' },
  { id: 'anz-independencia', name: 'Independencia', stateId: 'anzoategui' },
  { id: 'anz-jose_gregorio_monagas', name: 'José Gregorio Monagas', stateId: 'anzoategui' },
  { id: 'anz-juan_antonio_sotillo', name: 'Juan Antonio Sotillo', stateId: 'anzoategui' },
  { id: 'anz-juan_manuel_cajigal', name: 'Juan Manuel Cajigal', stateId: 'anzoategui' },
  { id: 'anz-libertad', name: 'Libertad', stateId: 'anzoategui' },
  { id: 'anz-manuel_ezequiel_bruzual', name: 'Manuel Ezequiel Bruzual', stateId: 'anzoategui' },
  { id: 'anz-pedro_maria_freites', name: 'Pedro María Freites', stateId: 'anzoategui' },
  { id: 'anz-piritu', name: 'Píritu', stateId: 'anzoategui' },
  { id: 'anz-san_jose_de_guanipa', name: 'San José de Guanipa', stateId: 'anzoategui' },
  { id: 'anz-san_juan_de_capistrano', name: 'San Juan de Capistrano', stateId: 'anzoategui' },
  { id: 'anz-santa_ana', name: 'Santa Ana', stateId: 'anzoategui' },
  { id: 'anz-simon_rodriguez', name: 'Simón Rodríguez', stateId: 'anzoategui' },
  { id: 'anz-sir_arthur_mc_gregor', name: 'Sir Arthur McGregor', stateId: 'anzoategui' },
  { id: 'anz-turismo', name: 'Turismo', stateId: 'anzoategui' },
  
  // Táchira
  { id: 'tac-san_cristobal', name: 'San Cristóbal', stateId: 'tachira' },
  { id: 'tac-cardenas', name: 'Cárdenas', stateId: 'tachira' },
  { id: 'tac-junin', name: 'Junín', stateId: 'tachira' },
  { id: 'tac-lobatera', name: 'Lobatera', stateId: 'tachira' },
  { id: 'tac-michelena', name: 'Michelena', stateId: 'tachira' },
  { id: 'tac-panamericano', name: 'Panamericano', stateId: 'tachira' },
  { id: 'tac-pedro_maria_ureña', name: 'Pedro María Ureña', stateId: 'tachira' },
  { id: 'tac-rafael_urdaneta', name: 'Rafael Urdaneta', stateId: 'tachira' },
  { id: 'tac-samuel_dario_maldonado', name: 'Samuel Darío Maldonado', stateId: 'tachira' },
  { id: 'tac-san_judas_tadeo', name: 'San Judas Tadeo', stateId: 'tachira' },
  { id: 'tac-uribante', name: 'Uribante', stateId: 'tachira' },
  { id: 'tac-antonio_romulo_costa', name: 'Antonio Rómulo Costa', stateId: 'tachira' },
  { id: 'tac-ayacucho', name: 'Ayacucho', stateId: 'tachira' },
  { id: 'tac-bolivar', name: 'Bolívar', stateId: 'tachira' },
  { id: 'tac-capacho_nuevo', name: 'Capacho Nuevo', stateId: 'tachira' },
  { id: 'tac-capacho_viejo', name: 'Capacho Viejo', stateId: 'tachira' },
  { id: 'tac-cordoba', name: 'Córdoba', stateId: 'tachira' },
  { id: 'tac-fernandez_feo', name: 'Fernández Feo', stateId: 'tachira' },
  { id: 'tac-francisco_de_miranda', name: 'Francisco de Miranda', stateId: 'tachira' },
  { id: 'tac-garcia_de_hevia', name: 'García de Hevia', stateId: 'tachira' },
  { id: 'tac-guasimos', name: 'Guásimos', stateId: 'tachira' },
  { id: 'tac-independencia', name: 'Independencia', stateId: 'tachira' },
  { id: 'tac-jauregui', name: 'Jáuregui', stateId: 'tachira' },
  { id: 'tac-jose_maria_vargas', name: 'José María Vargas', stateId: 'tachira' },
  { id: 'tac-libertad', name: 'Libertad', stateId: 'tachira' },
  { id: 'tac-libertador', name: 'Libertador', stateId: 'tachira' },
  { id: 'tac-seboruco', name: 'Seboruco', stateId: 'tachira' },
  { id: 'tac-sucre', name: 'Sucre', stateId: 'tachira' },
  { id: 'tac-torbes', name: 'Torbes', stateId: 'tachira' },

  // Amazonas
  { id: 'ama-alto_orinoco', name: 'Alto Orinoco', stateId: 'amazonas' },
  { id: 'ama-atabapo', name: 'Atabapo', stateId: 'amazonas' },
  { id: 'ama-Atures', name: 'Atures', stateId: 'amazonas' },
  { id: 'ama-manapiare', name: 'Manapiare', stateId: 'amazonas' },
  { id: 'ama-maroa', name: 'Maroa', stateId: 'amazonas' },
  { id: 'ama-rio_negro', name: 'Río Negro', stateId: 'amazonas' },
  { id: 'ama-autana', name: 'Autana', stateId: 'amazonas' },

  // Apure
  { id: 'apu-achaguas', name: 'Achaguas', stateId: 'apure' },
  { id: 'apu-biruaca', name: 'Biruaca', stateId: 'apure' },
  { id: 'apu-muñoz', name: 'Muñoz', stateId: 'apure' },
  { id: 'apu-paez', name: 'Páez', stateId: 'apure' },
  { id: 'apu-romulo_gallegos', name: 'Rómulo Gallegos', stateId: 'apure' },
  { id: 'apu-san_fernando', name: 'San Fernando', stateId: 'apure' },
  { id: 'apu-pedro_camejo', name: 'Pedro Camejo', stateId: 'apure' },

  // Barinas
  { id: 'bar-alberto_arvelo_torrealba', name: 'Alberto Arvelo Torrealba', stateId: 'barinas' },
  { id: 'bar-andraujo', name: 'Andrés Eloy Blanco', stateId: 'barinas' },
  { id: 'bar-antonio_jose_de_sucre', name: 'Antonio José de Sucre', stateId: 'barinas' },
  { id: 'bar-arismendi', name: 'Arismendi', stateId: 'barinas' },
  { id: 'bar-barinas', name: 'Barinas', stateId: 'barinas' },
  { id: 'bar-bolivar', name: 'Bolívar', stateId: 'barinas' },
  { id: 'bar-cruz_paredes', name: 'Cruz Paredes', stateId: 'barinas' },
  { id: 'bar-ezequiel_zamora', name: 'Ezequiel Zamora', stateId: 'barinas' },
  { id: 'bar-obispo', name: 'Obispo', stateId: 'barinas' },
  { id: 'bar-pedraza', name: 'Pedraza', stateId: 'barinas' },
  { id: 'bar-rojas', name: 'Rojas', stateId: 'barinas' },
  { id: 'bar-sosa', name: 'Sosa', stateId: 'barinas' },

  // Bolívar
  { id: 'bol-caroni', name: 'Caroní', stateId: 'bolivar' },
  { id: 'bol-ceres', name: 'Cedeño', stateId: 'bolivar' },
  { id: 'bol-el_callao', name: 'El Callao', stateId: 'bolivar' },
  { id: 'bol-gran_sabana', name: 'Gran Sabana', stateId: 'bolivar' },
  { id: 'bol-heres', name: 'Heres', stateId: 'bolivar' },
  { id: 'bol-jachira', name: 'Piar', stateId: 'bolivar' },
  { id: 'bol-padre_pedro_chien', name: 'Padre Pedro Chien', stateId: 'bolivar' },
  { id: 'bol-raul_leoni', name: 'Raúl Leoni', stateId: 'bolivar' },
  { id: 'bol-rosio', name: 'Roscio', stateId: 'bolivar' },
  { id: 'bol-sifontes', name: 'Sifontes', stateId: 'bolivar' },
  { id: 'bol-sucre', name: 'Sucre', stateId: 'bolivar' },

  // Cojedes
  { id: 'coj-anzoategui', name: 'Anzoátegui', stateId: 'cojedes' },
  { id: 'coj-falcon', name: 'Falcón', stateId: 'cojedes' },
  { id: 'coj-girardot', name: 'Girardot', stateId: 'cojedes' },
  { id: 'coj-lima_blanco', name: 'Lima Blanco', stateId: 'cojedes' },
  { id: 'coj-ricaurte', name: 'Ricaurte', stateId: 'cojedes' },
  { id: 'coj-romulo_gallegos', name: 'Rómulo Gallegos', stateId: 'cojedes' },
  { id: 'coj-san_carlos', name: 'San Carlos', stateId: 'cojedes' },
  { id: 'coj-tinaco', name: 'Tinaco', stateId: 'cojedes' },
  { id: 'coj-tinaquillo', name: 'Tinaquillo', stateId: 'cojedes' },

  // Delta Amacuro
  { id: 'dam-antonio_diaz', name: 'Antonio Díaz', stateId: 'delta_amacuro' },
  { id: 'dam-casacoima', name: 'Casacoima', stateId: 'delta_amacuro' },
  { id: 'dam-pedernales', name: 'Pedernales', stateId: 'delta_amacuro' },
  { id: 'dam-tucupita', name: 'Tucupita', stateId: 'delta_amacuro' },

  // Falcón
  { id: 'fal-acosta', name: 'Acosta', stateId: 'falcon' },
  { id: 'fal-bolivar', name: 'Bolívar', stateId: 'falcon' },
  { id: 'fal-buchivacoa', name: 'Buchivacoa', stateId: 'falcon' },
  { id: 'fal-carirubana', name: 'Carirubana', stateId: 'falcon' },
  { id: 'fal-colina', name: 'Colina', stateId: 'falcon' },
  { id: 'fal-dabajuro', name: 'Dabajuro', stateId: 'falcon' },
  { id: 'fal-democracia', name: 'Democracia', stateId: 'falcon' },
  { id: 'fal-falcon', name: 'Falcón', stateId: 'falcon' },
  { id: 'fal-federacion', name: 'Federación', stateId: 'falcon' },
  { id: 'fal-jaimito', name: 'Jacura', stateId: 'falcon' },
  { id: 'fal-los_taques', name: 'Los Taques', stateId: 'falcon' },
  { id: 'fal-mauroa', name: 'Mauroa', stateId: 'falcon' },
  { id: 'fal-miranda', name: 'Miranda', stateId: 'falcon' },
  { id: 'fal-monseñor_iturriza', name: 'Monseñor Iturriza', stateId: 'falcon' },
  { id: 'fal-palmasola', name: 'Palmasola', stateId: 'falcon' },
  { id: 'fal-petit', name: 'Petit', stateId: 'falcon' },
  { id: 'fal-piritu', name: 'Píritu', stateId: 'falcon' },
  { id: 'fal-san_francisco', name: 'San Francisco', stateId: 'falcon' },
  { id: 'fal-san_jose_de_falcon', name: 'San José de Falcón', stateId: 'falcon' },
  { id: 'fal-san_luis', name: 'San Luis', stateId: 'falcon' },
  { id: 'fal-silva', name: 'Silva', stateId: 'falcon' },
  { id: 'fal-sucre', name: 'Sucre', stateId: 'falcon' },
  { id: 'fal-tocopero', name: 'Tocopero', stateId: 'falcon' },
  { id: 'fal-union', name: 'Unión', stateId: 'falcon' },
  { id: 'fal-uruma', name: 'Urumaco', stateId: 'falcon' },
  { id: 'fal-zamora', name: 'Zamora', stateId: 'falcon' },

  // Guárico
  { id: 'gua-camaguan', name: 'Camaguán', stateId: 'guarico' },
  { id: 'gua-chaguaramas', name: 'Chaguaramas', stateId: 'guarico' },
  { id: 'gua-el_socorro', name: 'El Socorro', stateId: 'guarico' },
  { id: 'gua-guaribe', name: 'Guaribe', stateId: 'guarico' },
  { id: 'gua-infante', name: 'Infante', stateId: 'guarico' },
  { id: 'gua-las_mercedes', name: 'Las Mercedes', stateId: 'guarico' },
  { id: 'gua-monagas', name: 'Monagas', stateId: 'guarico' },
  { id: 'gua-ortiz', name: 'Ortiz', stateId: 'guarico' },
  { id: 'gua-ribas', name: 'Ribas', stateId: 'guarico' },
  { id: 'gua-rorro', name: 'Roscio', stateId: 'guarico' },
  { id: 'gua-san_jeronimo_de_guayabal', name: 'San Jerónimo de Guayabal', stateId: 'guarico' },
  { id: 'gua-san_jose_de_guaribe', name: 'San José de Guaribe', stateId: 'guarico' },
  { id: 'gua-santa_maria_de_ipire', name: 'Santa María de Ipire', stateId: 'guarico' },
  { id: 'gua-zaraza', name: 'Zaraza', stateId: 'guarico' },

  // La Guaira
  { id: 'lag-vargas', name: 'Vargas', stateId: 'la_guaira' },

  // Mérida
  { id: 'mer-Alberto_Adriani', name: 'Alberto Adriani', stateId: 'merida' },
  { id: 'mer-Andrés_Bello', name: 'Andrés Bello', stateId: 'merida' },
  { id: 'mer-Antonio_Pinto_Salinas', name: 'Antonio Pinto Salinas', stateId: 'merida' },
  { id: 'mer-Aricagua', name: 'Aricagua', stateId: 'merida' },
  { id: 'mer-Arzobispo_Chacón', name: 'Arzobispo Chacón', stateId: 'merida' },
  { id: 'mer-Campo_Elías', name: 'Campo Elías', stateId: 'merida' },
  { id: 'mer-Caracciolo_Parra_Olmedo', name: 'Caracciolo Parra Olmedo', stateId: 'merida' },
  { id: 'mer-Cardenal_Quintero', name: 'Cardenal Quintero', stateId: 'merida' },
  { id: 'mer-Guaraque', name: 'Guaraque', stateId: 'merida' },
  { id: 'mer-Jáuregui', name: 'Jáuregui', stateId: 'merida' },
  { id: 'mer-Julio_César_Salas', name: 'Julio César Salas', stateId: 'merida' },
  { id: 'mer-Justo_Briceño', name: 'Justo Briceño', stateId: 'merida' },
  { id: 'mer-Libertador', name: 'Libertador', stateId: 'merida' },
  { id: 'mer-Miranda', name: 'Miranda', stateId: 'merida' },
  { id: 'mer-Obispo_Ramos_de_Lora', name: 'Obispo Ramos de Lora', stateId: 'merida' },
  { id: 'mer-Padre_Noguera', name: 'Padre Noguera', stateId: 'merida' },
  { id: 'mer-Pueblo_Llano', name: 'Pueblo Llano', stateId: 'merida' },
  { id: 'mer-Rangel', name: 'Rangel', stateId: 'merida' },
  { id: 'mer-Rivas_Dávila', name: 'Rivas Dávila', stateId: 'merida' },
  { id: 'mer-Santos_Marquina', name: 'Santos Marquina', stateId: 'merida' },
  { id: 'mer-Sucre', name: 'Sucre', stateId: 'merida' },
  { id: 'mer-Tovar', name: 'Tovar', stateId: 'merida' },
  { id: 'mer-Tulio_Febres_Cordero', name: 'Tulio Febres Cordero', stateId: 'merida' },
  { id: 'mer-Zea', name: 'Zea', stateId: 'merida' },

  // Monagas
  { id: 'mon-Acosta', name: 'Acosta', stateId: 'monagas' },
  { id: 'mon-Aguasay', name: 'Aguasay', stateId: 'monagas' },
  { id: 'mon-Bolívar', name: 'Bolívar', stateId: 'monagas' },
  { id: 'mon-Caripe', name: 'Caripe', stateId: 'monagas' },
  { id: 'mon-Cedeño', name: 'Cedeño', stateId: 'monagas' },
  { id: 'mon-Ezequiel_Zamora', name: 'Ezequiel Zamora', stateId: 'monagas' },
  { id: 'mon-Libertador', name: 'Libertador', stateId: 'monagas' },
  { id: 'mon-Maturín', name: 'Maturín', stateId: 'monagas' },
  { id: 'mon-Piar', name: 'Piar', stateId: 'monagas' },
  { id: 'mon-Punceres', name: 'Punceres', stateId: 'monagas' },
  { id: 'mon-Santa_Bárbara', name: 'Santa Bárbara', stateId: 'monagas' },
  { id: 'mon-Sotillo', name: 'Sotillo', stateId: 'monagas' },
  { id: 'mon-Uracoa', name: 'Uracoa', stateId: 'monagas' },

  // Nueva Esparta
  { id: 'nue-Antolín_del_Campo', name: 'Antolín del Campo', stateId: 'nueva_esparta' },
  { id: 'nue-Arismendi', name: 'Arismendi', stateId: 'nueva_esparta' },
  { id: 'nue-Díaz', name: 'Díaz', stateId: 'nueva_esparta' },
  { id: 'nue-García', name: 'García', stateId: 'nueva_esparta' },
  { id: 'nue-Gómez', name: 'Gómez', stateId: 'nueva_esparta' },
  { id: 'nue-Maneiro', name: 'Maneiro', stateId: 'nueva_esparta' },
  { id: 'nue-Marcano', name: 'Marcano', stateId: 'nueva_esparta' },
  { id: 'nue-Mariño', name: 'Mariño', stateId: 'nueva_esparta' },
  { id: 'nue-Península_de_Macanao', name: 'Península de Macanao', stateId: 'nueva_esparta' },
  { id: 'nue-Tubores', name: 'Tubores', stateId: 'nueva_esparta' },
  { id: 'nue-Villalba', name: 'Villalba', stateId: 'nueva_esparta' },

  // Portuguesa
  { id: 'por-Agua_Blanca', name: 'Agua Blanca', stateId: 'portuguesa' },
  { id: 'por-Araure', name: 'Araure', stateId: 'portuguesa' },
  { id: 'por-Guanare', name: 'Guanare', stateId: 'portuguesa' },
  { id: 'por-Génesis_de_Canelon', name: 'Génesis de Canelon', stateId: 'portuguesa' },
  { id: 'por-José_Vicente_de_Unda', name: 'José Vicente de Unda', stateId: 'portuguesa' },
  { id: 'por-Ospino', name: 'Ospino', stateId: 'portuguesa' },
  { id: 'por-Páez', name: 'Páez', stateId: 'portuguesa' },
  { id: 'por-Papelón', name: 'Papelón', stateId: 'portuguesa' },
  { id: 'por-San_Genaro_de_Boconoito', name: 'San Genaro de Boconoito', stateId: 'portuguesa' },
  { id: 'por-San_Rafael_de_Onoto', name: 'San Rafael de Onoto', stateId: 'portuguesa' },
  { id: 'por-Santa_Rosalía', name: 'Santa Rosalía',stateId: 'portuguesa' },
  { id: 'por-Turén', name: 'Turén', stateId: 'portuguesa' },

  // Sucre
  { id: 'suc-Andrés_Eloy_Blanco', name: 'Andrés Eloy Blanco', stateId: 'sucre' },
  { id: 'suc-Andrés_Mata', name: 'Andrés Mata', stateId: 'sucre' },
  { id: 'suc-Arismendi', name: 'Arismendi', stateId: 'sucre' },
  { id: 'suc-Benítez', name: 'Benítez', stateId: 'sucre' },
  { id: 'suc-Bermúdez', name: 'Bermúdez', stateId: 'sucre' },
  { id: 'suc-Bolívar', name: 'Bolívar', stateId: 'sucre' },
  { id: 'suc-Cajigal', name: 'Cajigal', stateId: 'sucre' },
  { id: 'suc-Cruz_Salmerón_Acosta', name: 'Cruz Salmerón Acosta', stateId: 'sucre' },
  { id: 'suc-Libertador', name: 'Libertador', stateId: 'sucre' },
  { id: 'suc-Mariño', name: 'Mariño', stateId: 'sucre' },
  { id: 'suc-Mejía', name: 'Mejía', stateId: 'sucre' },
  { id: 'suc-Montes', name: 'Montes', stateId: 'sucre' },
  { id: 'suc-Ribero', name: 'Ribero', stateId: 'sucre' },
  { id: 'suc-Sucre', name: 'Sucre', stateId: 'sucre' },
  { id: 'suc-Valdez', name: 'Valdez', stateId: 'sucre' },

  // Trujillo
  { id: 'tru-Andrés_Bello', name: 'Andrés Bello', stateId: 'trujillo' },
  { id: 'tru-Boconó', name: 'Boconó', stateId: 'trujillo' },
  { id: 'tru-Bolívar', name: 'Bolívar', stateId: 'trujillo' },
  { id: 'tru-Candelaria', name: 'Candelaria', stateId: 'trujillo' },
  { id: 'tru-Carache', name: 'Carache', stateId: 'trujillo' },
  { id: 'tru-Escuque', name: 'Escuque', stateId: 'trujillo' },
  { id: 'tru-José_Felipe_Márquez_Cañizales', name: 'José Felipe Márquez Cañizales', stateId: 'trujillo' },
  { id: 'tru-Juan_Vicente_Campos_Elías', name: 'Juan Vicente Campos Elías', stateId: 'trujillo' },
  { id: 'tru-La_Ceiba', name: 'La Ceiba', stateId: 'trujillo' },
  { id: 'tru-Miranda', name: 'Miranda', stateId: 'trujillo' },
  { id: 'tru-Monte_San_Rafael_de_Carvajal', name: 'Monte San Rafael de Carvajal', stateId: 'trujillo' },
  { id: 'tru-Motatán', name: 'Motatán', stateId: 'trujillo' },
  { id: 'tru-Pampán', name: 'Pampán', stateId: 'trujillo' },
  { id: 'tru-Pampanito', name: 'Pampanito', stateId: 'trujillo' },
  { id: 'tru-Rafael_Rangel', name: 'Rafael Rangel', stateId: 'trujillo' },
  { id: 'tru-San_Rafael_de_Carvajal', name: 'San Rafael de Carvajal', stateId: 'trujillo' },
  { id: 'tru-Trujillo', name: 'Trujillo', stateId: 'trujillo' },
  { id: 'tru-Urdaneta', name: 'Urdaneta', stateId: 'trujillo' },
  { id: 'tru-Valera', name: 'Valera', stateId: 'trujillo' },
  { id: 'tru-Padre_Honorio_Jiménez', name: 'Padre Honorio Jiménez', stateId: 'trujillo' },

  // Yaracuy
  { id: 'yar-Arístides_Bastidas', name: 'Arístides Bastidas', stateId: 'yaracuy' },
  { id: 'yar-Bolívar', name: 'Bolívar', stateId: 'yaracuy' },
  { id: 'yar-Bruzual', name: 'Bruzual', stateId: 'yaracuy' },
  { id: 'yar-Cocorote', name: 'Cocorote', stateId: 'yaracuy' },
  { id: 'yar-Independencia', name: 'Independencia', stateId: 'yaracuy' },
  { id: 'yar-José_Antonio_Páez', name: 'José Antonio Páez', stateId: 'yaracuy' },
  { id: 'yar-La_Trinidad', name: 'La Trinidad', stateId: 'yaracuy' },
  { id: 'yar-Manuel_Monge', name: 'Manuel Monge', stateId: 'yaracuy' },
  { id: 'yar-Nirgua', name: 'Nirgua', stateId: 'yaracuy' },
  { id: 'yar-Peña', name: 'Peña', stateId: 'yaracuy' },
  { id: 'yar-San_Felipe', name: 'San Felipe', stateId: 'yaracuy' },
  { id: 'yar-Sucre', name: 'Sucre', stateId: 'yaracuy' },
  { id: 'yar-Urachiche', name: 'Urachiche', stateId: 'yaracuy' },
  { id: 'yar-Veroes', name: 'Veroes', stateId: 'yaracuy' },

  // Dependencias Federales
  { id: 'dep-dependencias_federales', name: 'Dependencias Federales', stateId: 'dependencias_federales' }
];
