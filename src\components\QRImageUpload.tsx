// src/components/QRImageUpload.tsx
import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { CameraIcon, QrCodeIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useToast } from './ToastProvider';
import { qrService, type ExtractedData, type QRProgress } from '../services/qrService';

interface QRImageUploadProps {
  onDataExtracted: (data: ExtractedData) => void;
  isProcessing: boolean;
  onProcessingChange: (processing: boolean) => void;
}

const QRImageUpload: React.FC<QRImageUploadProps> = ({
  onDataExtracted,
  isProcessing,
  onProcessingChange
}) => {
  const { showToast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [qrProgress, setQrProgress] = useState<QRProgress | null>(null);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      showToast('Formato no válido', 'Por favor seleccione una imagen JPG, PNG o WebP', 'error');
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      showToast('Archivo muy grande', 'El archivo debe ser menor a 10MB', 'error');
      return;
    }

    setSelectedImage(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Process image with QR Scanner
  const processImage = async () => {
    if (!selectedImage) return;

    onProcessingChange(true);
    setQrProgress({ status: 'Iniciando...', progress: 0 });

    try {
      // Scan QR code from image
      const qrResult = await qrService.scanQRFromImage(
        selectedImage,
        (progress) => setQrProgress(progress)
      );

      if (!qrResult.success) {
        showToast(
          'QR no encontrado', 
          'No se pudo encontrar un código QR en la imagen.', 
          'warning'
        );
        return;
      }

      // Extract structured data from QR content
      const extractedData = qrService.extractStructuredData(qrResult.data);

      if (extractedData.confidence < 20) {
        showToast(
          'Datos no encontrados',
          'El código QR no contiene datos de vehículo válidos.',
          'warning'
        );
      } else {
        showToast(
          'QR leído exitosamente',
          `Se extrajeron datos con ${Math.round(extractedData.confidence)}% de confianza`,
          'success'
        );
      }

      onDataExtracted(extractedData);

    } catch (error) {
      console.error('QR Scan Error:', error);
      showToast(
        'Error de lectura',
        'No se pudo leer el código QR. Verifique que la imagen contenga un QR válido.',
        'error'
      );
    } finally {
      onProcessingChange(false);
      setQrProgress(null);
    }
  };

  // Clear selected image
  const clearImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setQrProgress(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Trigger file input
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <Container>
      <Header>
        <HeaderIcon>
          <QrCodeIcon />
        </HeaderIcon>
        <HeaderText>
          <HeaderTitle>Lectura de Código QR</HeaderTitle>
          <HeaderSubtitle>
            Suba una imagen con código QR para completar automáticamente el formulario
          </HeaderSubtitle>
        </HeaderText>
      </Header>

      <UploadArea>
        {!selectedImage ? (
          <UploadButton onClick={(e) => {
            e.preventDefault();
            triggerFileInput();
          }} disabled={isProcessing}>
            <CameraIcon />
            <span>Seleccionar Imagen</span>
          </UploadButton>
        ) : (
          <ImagePreviewContainer>
            <ImagePreview src={imagePreview!} alt="Imagen con QR seleccionada" />
            <ImageActions>
              <ActionButton onClick={clearImage} disabled={isProcessing}>
                <XMarkIcon />
                Cambiar
              </ActionButton>
              <ActionButton 
                onClick={processImage} 
                disabled={isProcessing}
                $primary
              >
                <QrCodeIcon />
                {isProcessing ? 'Leyendo QR...' : 'Leer QR'}
              </ActionButton>
            </ImageActions>
          </ImagePreviewContainer>
        )}

        {qrProgress && (
          <ProgressContainer>
            <ProgressText>{qrProgress.status}</ProgressText>
            <ProgressBar>
              <ProgressFill $progress={qrProgress.progress} />
            </ProgressBar>
            <ProgressPercentage>{qrProgress.progress}%</ProgressPercentage>
          </ProgressContainer>
        )}
      </UploadArea>

      <Instructions>
        <InstructionTitle>Consejos para mejores resultados:</InstructionTitle>
        <InstructionList>
          <li>Asegúrese de que el código QR esté completo y visible</li>
          <li>Use buena iluminación y evite reflejos</li>
          <li>Mantenga la imagen enfocada y sin distorsión</li>
          <li>Formatos soportados: JPG, PNG, WebP (máx. 10MB)</li>
        </InstructionList>
      </Instructions>

      <HiddenFileInput
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp"
        onChange={handleFileSelect}
      />
    </Container>
  );
};

// Styled Components (same as before)
const Container = styled.div`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  padding: ${props => props.theme.space[6]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  margin-bottom: ${props => props.theme.space[6]};
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[3]};
  margin-bottom: ${props => props.theme.space[6]};
`;

const HeaderIcon = styled.div`
  width: 24px;
  height: 24px;
  color: ${props => props.theme.colors.primary};

  svg {
    width: 100%;
    height: 100%;
  }
`;

const HeaderText = styled.div`
  flex: 1;
`;

const HeaderTitle = styled.h3`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.space[1]};
`;

const HeaderSubtitle = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  margin: 0;
`;

const UploadArea = styled.div`
  border: 2px dashed ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  padding: ${props => props.theme.space[6]};
  text-align: center;
  margin-bottom: ${props => props.theme.space[4]};
`;

const UploadButton = styled.button`
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[4]};
  background: none;
  border: none;
  color: ${props => props.theme.colors.primary};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 48px;
    height: 48px;
  }

  span {
    font-size: ${props => props.theme.fontSizes.md};
    font-weight: ${props => props.theme.fontWeights.medium};
  }

  &:hover:not(:disabled) {
    color: ${props => props.theme.colors.primaryHover};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ImagePreviewContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.space[4]};
`;

const ImagePreview = styled.img`
  max-width: 300px;
  max-height: 200px;
  border-radius: ${props => props.theme.radii.md};
  box-shadow: ${props => props.theme.shadows.sm};
`;

const ImageActions = styled.div`
  display: flex;
  gap: ${props => props.theme.space[3]};
`;

const ActionButton = styled.button<{ $primary?: boolean }>`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[4]};
  border: 1px solid ${props => props.$primary ? props.theme.colors.primary : props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  background-color: ${props => props.$primary ? props.theme.colors.primary : 'transparent'};
  color: ${props => props.$primary ? 'white' : props.theme.colors.text};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 16px;
    height: 16px;
  }

  &:hover:not(:disabled) {
    background-color: ${props => props.$primary ? props.theme.colors.primaryHover : props.theme.colors.backgroundMuted};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ProgressContainer = styled.div`
  margin-top: ${props => props.theme.space[4]};
  text-align: center;
`;

const ProgressText = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  margin-bottom: ${props => props.theme.space[2]};
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.full};
  overflow: hidden;
  margin-bottom: ${props => props.theme.space[1]};
`;

const ProgressFill = styled.div<{ $progress: number }>`
  width: ${props => props.$progress}%;
  height: 100%;
  background-color: ${props => props.theme.colors.primary};
  transition: width 0.3s ease;
`;

const ProgressPercentage = styled.div`
  font-size: ${props => props.theme.fontSizes.xs};
  color: ${props => props.theme.colors.textMuted};
`;

const Instructions = styled.div`
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  padding: ${props => props.theme.space[4]};
`;

const InstructionTitle = styled.h4`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.space[2]};
`;

const InstructionList = styled.ul`
  font-size: ${props => props.theme.fontSizes.xs};
  color: ${props => props.theme.colors.textLight};
  margin: 0;
  padding-left: ${props => props.theme.space[4]};

  li {
    margin-bottom: ${props => props.theme.space[1]};
  }
`;

const HiddenFileInput = styled.input`
  display: none;
`;

export default QRImageUpload;
