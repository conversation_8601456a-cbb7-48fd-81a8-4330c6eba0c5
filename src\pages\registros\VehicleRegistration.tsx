// src/pages/registros/VehicleRegistration.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import * as Form from '@radix-ui/react-form';
import { UserIcon, TruckIcon, DocumentTextIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { useToast } from '../../components/ToastProvider';
import LoadingOverlay from '../../components/LoadingOverlay';
import FileUpload from '../../components/FileUpload';
import ConfirmationModal from '../../components/ConfirmationModal';
// import QRImageUpload from '../../components/QRImageUpload';
import { VehicleApiService, type VehicleRegistrationData } from '../../services/vehicleApi';
// import { qrService, type ExtractedData } from '../../services/qrService';
import { states } from '../../data/states';
import { municipalities } from '../../data/municipalities';
import { vehicleTypes } from '../../data/vehicleType';
import { vehicleUseTypes } from '../../data/vehicleUseType';

const VehicleRegistration: React.FC = () => {
  const { showToast } = useToast();

  // Form state
  const [formData, setFormData] = useState<VehicleRegistrationData>({
    documentType: 'V',
    rifNumber: '',
    nameOrBusinessName: '',
    contributorSince: '',
    vehicleUseType: '',
    vehicleType: '',
    state: '',
    municipality: '',
    brand: '',
    model: '',
    year: new Date().getFullYear(),
    color: '',
    plateNumber: '',
    chassisNumber: '',
    engineNumber: '',
  });

  // UI state
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [filteredMunicipalities, setFilteredMunicipalities] = useState(municipalities);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // QR state
  const [isQRProcessing, setIsQRProcessing] = useState(false);
  const [autoFilledFields, setAutoFilledFields] = useState<Set<string>>(new Set());

  // Filter municipalities when state changes
  useEffect(() => {
    if (formData.state) {
      const filtered = municipalities.filter(m => m.stateId === formData.state);
      setFilteredMunicipalities(filtered);

      // Reset municipality if it doesn't belong to the selected state
      if (formData.municipality && !filtered.find(m => m.id === formData.municipality)) {
        setFormData(prev => ({ ...prev, municipality: '' }));
      }
    } else {
      setFilteredMunicipalities([]);
      setFormData(prev => ({ ...prev, municipality: '' }));
    }
  }, [formData.state, formData.municipality]);

  // Handle input changes
  const handleInputChange = (field: keyof VehicleRegistrationData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Remove from auto-filled fields when user manually edits
    if (autoFilledFields.has(field)) {
      setAutoFilledFields(prev => {
        const newSet = new Set(prev);
        newSet.delete(field);
        return newSet;
      });
    }
  };

  // Handle QR data extraction
  // const handleOCRDataExtracted = (extractedData: ExtractedData) => {
  //   const newAutoFilledFields = new Set<string>();
  //   const updates: Partial<VehicleRegistrationData> = {};

  //   // Map extracted data to form fields
  //   if (extractedData.rifNumber) {
  //     updates.rifNumber = extractedData.rifNumber;
  //     newAutoFilledFields.add('rifNumber');
  //   }

  //   if (extractedData.nameOrBusinessName) {
  //     updates.nameOrBusinessName = extractedData.nameOrBusinessName;
  //     newAutoFilledFields.add('nameOrBusinessName');
  //   }

  //   if (extractedData.plateNumber) {
  //     updates.plateNumber = extractedData.plateNumber;
  //     newAutoFilledFields.add('plateNumber');
  //   }

  //   if (extractedData.chassisNumber) {
  //     updates.chassisNumber = extractedData.chassisNumber;
  //     newAutoFilledFields.add('chassisNumber');
  //   }

  //   if (extractedData.engineNumber) {
  //     updates.engineNumber = extractedData.engineNumber;
  //     newAutoFilledFields.add('engineNumber');
  //   }

  //   if (extractedData.brand) {
  //     updates.brand = extractedData.brand;
  //     newAutoFilledFields.add('brand');
  //   }

  //   if (extractedData.model) {
  //     updates.model = extractedData.model;
  //     newAutoFilledFields.add('model');
  //   }

  //   if (extractedData.year) {
  //     updates.year = extractedData.year;
  //     newAutoFilledFields.add('year');
  //   }

  //   if (extractedData.color) {
  //     updates.color = extractedData.color;
  //     newAutoFilledFields.add('color');
  //   }

  //   if (extractedData.vehicleType) {
  //     updates.vehicleType = extractedData.vehicleType;
  //     newAutoFilledFields.add('vehicleType');
  //   }

  //   if (extractedData.vehicleUseType) {
  //     updates.vehicleUseType = extractedData.vehicleUseType;
  //     newAutoFilledFields.add('vehicleUseType');
  //   }

  //   // Try to match vehicle use type and vehicle type using fuzzy search
  //   if (extractedData.brand) {
  //     const vehicleTypeMatch = qrService.findBestMatch(extractedData.brand, vehicleTypes);
  //     if (vehicleTypeMatch) {
  //       updates.vehicleType = vehicleTypeMatch;
  //       newAutoFilledFields.add('vehicleType');
  //     }
  //   }

  //   setFormData(prev => ({ ...prev, ...updates }));
  //   setAutoFilledFields(newAutoFilledFields);

  //   const clearedErrors = { ...errors };
  //   newAutoFilledFields.forEach(field => {
  //     delete clearedErrors[field];
  //   });
  //   setErrors(clearedErrors);
  // };

  // Reset form to initial state
  const resetForm = () => {
    // Clear all form fields
    setFormData({
      documentType: 'V',
      rifNumber: '',
      nameOrBusinessName: '',
      contributorSince: '',
      vehicleUseType: '',
      vehicleType: '',
      state: '',
      municipality: '',
      brand: '',
      model: '',
      year: new Date().getFullYear(),
      color: '',
      plateNumber: '',
      chassisNumber: '',
      engineNumber: '',
    });

    // Reset file upload
    setSelectedFile(null);

    // Clear all validation errors
    setErrors({});

    // Clear QR state
    setAutoFilledFields(new Set());
  };

  // Handle file selection
  const handleFileSelect = (file: File | null) => {
    setSelectedFile(file);
    if (file) {
      setFormData(prev => ({ ...prev, documentFile: file }));
    } else {
      setFormData(prev => {
        const { documentFile, ...rest } = prev;
        return rest;
      });
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Owner information validation
    if (!formData.rifNumber.trim()) newErrors.rifNumber = 'El número de RIF es requerido';
    if (!formData.nameOrBusinessName.trim()) newErrors.nameOrBusinessName = 'El nombre o razón social es requerido';
    if (!formData.contributorSince.trim()) newErrors.contributorSince = 'La fecha de contribuyente desde es requerida';

    // Vehicle information validation
    if (!formData.vehicleUseType) newErrors.vehicleUseType = 'El tipo de uso es requerido';
    if (!formData.vehicleType) newErrors.vehicleType = 'El tipo de vehículo es requerido';
    if (!formData.state) newErrors.state = 'El estado es requerido';
    if (!formData.municipality) newErrors.municipality = 'El municipio es requerido';
    if (!formData.brand.trim()) newErrors.brand = 'La marca es requerida';
    if (!formData.model.trim()) newErrors.model = 'El modelo es requerido';
    if (!formData.year || formData.year < 1900 || formData.year > new Date().getFullYear() + 1) {
      newErrors.year = 'Ingrese un año válido';
    }
    if (!formData.color.trim()) newErrors.color = 'El color es requerido';
    if (!formData.plateNumber.trim()) newErrors.plateNumber = 'El número de placa es requerido';
    if (!formData.chassisNumber.trim()) newErrors.chassisNumber = 'El número de chasis es requerido';
    if (!formData.engineNumber.trim()) newErrors.engineNumber = 'El número de motor es requerido';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      showToast('Error de validación', 'Por favor, corrija los errores en el formulario', 'error');
      return;
    }

    setShowConfirmModal(true);
  };

  // Handle confirmation
  const handleConfirm = async () => {
    setIsLoading(true);

    try {
      const response = await VehicleApiService.registerVehicle(formData);

      if (response.success) {
        // 1. Close the confirmation modal
        setShowConfirmModal(false);

        // 2, 3, 4. Clear all form fields, reset file upload, and clear validation errors
        resetForm();

        // 5. Show success notification
        showToast(
          'Registro exitoso',
          'Vehículo registrado exitosamente',
          'success'
        );

        // 6. Hide loading state (handled in finally block)
      } else {
        showToast('Error', response.error || 'Ha ocurrido un error', 'error');
      }
    } catch (error) {
      showToast('Error', 'Ha ocurrido un error inesperado', 'error');
    } finally {
      // 5. Hide loading state - Ensure loading overlay is dismissed
      setIsLoading(false);
    }
  };

  return (
    <>
      <Container>
        <Header>
          <Title>Registro de Vehículos</Title>
          <Subtitle>Complete el formulario para registrar un nuevo vehículo</Subtitle>
        </Header>

        <StyledForm onSubmit={handleSubmit}>
          {/* QR Section */}
          {/* <QRImageUpload
            onDataExtracted={handleOCRDataExtracted}
            isProcessing={isQRProcessing}
            onProcessingChange={setIsQRProcessing}
          /> */}

          {/* Owner Information Section */}
          <Section>
            <SectionHeader>
              <SectionIcon>
                <UserIcon />
              </SectionIcon>
              <SectionTitle>Información del Propietario</SectionTitle>
            </SectionHeader>

            <FormGrid>
              <FormField name="documentType">
                <FormLabel>Tipo de Documento *</FormLabel>
                <Select
                  value={formData.documentType}
                  onChange={(e) => handleInputChange('documentType', e.target.value)}
                >
                  <option value="V">V - Venezolano</option>
                  <option value="E">E - Extranjero</option>
                  <option value="J">J - Jurídico</option>
                  <option value="P">P - Pasaporte</option>
                </Select>
              </FormField>

              <FormField name="rifNumber">
                <FormLabel>Número de doc *</FormLabel>
                <Input
                  type="text"
                  placeholder="12345678-9"
                  value={formData.rifNumber}
                  onChange={(e) => handleInputChange('rifNumber', e.target.value.toUpperCase())}
                  $hasError={!!errors.rifNumber}
                  $autoFilled={autoFilledFields.has('rifNumber')}
                />
                {errors.rifNumber && <ErrorMessage>{errors.rifNumber}</ErrorMessage>}
                {autoFilledFields.has('rifNumber') && (
                  <AutoFilledIndicator>
                    <SparklesIcon />
                    Completado automáticamente
                  </AutoFilledIndicator>
                )}
              </FormField>

              <FormField name="nameOrBusinessName">
                <FormLabel>Nombre o Razón Social *</FormLabel>
                <Input
                  type="text"
                  placeholder="Ingrese el nombre o razón social"
                  value={formData.nameOrBusinessName}
                  onChange={(e) => handleInputChange('nameOrBusinessName', e.target.value)}
                  $hasError={!!errors.nameOrBusinessName}
                  $autoFilled={autoFilledFields.has('nameOrBusinessName')}
                />
                {errors.nameOrBusinessName && <ErrorMessage>{errors.nameOrBusinessName}</ErrorMessage>}
                {autoFilledFields.has('nameOrBusinessName') && (
                  <AutoFilledIndicator>
                    <SparklesIcon />
                    Completado automáticamente
                  </AutoFilledIndicator>
                )}
              </FormField>

              <FormField name="contributorSince">
                <FormLabel>Contribuyente desde *</FormLabel>
                <Input
                  type="date"
                  value={formData.contributorSince}
                  onChange={(e) => handleInputChange('contributorSince', e.target.value)}
                  $hasError={!!errors.contributorSince}
                />
                {errors.contributorSince && <ErrorMessage>{errors.contributorSince}</ErrorMessage>}
              </FormField>
            </FormGrid>
          </Section>

          {/* Vehicle Information Section */}
          <Section>
            <SectionHeader>
              <SectionIcon>
                <TruckIcon />
              </SectionIcon>
              <SectionTitle>Información del Vehículo</SectionTitle>
            </SectionHeader>

            <FormGrid>
              <FormField name="vehicleUseType">
                <FormLabel>Tipo de uso *</FormLabel>
                <Select
                  value={formData.vehicleUseType}
                  onChange={(e) => handleInputChange('vehicleUseType', e.target.value)}
                  $hasError={!!errors.vehicleUseType}
                >
                  <option value="">Seleccione el tipo de uso</option>
                  {vehicleUseTypes.map(useType => (
                    <option key={useType.id} value={useType.id}>
                      {useType.name}
                    </option>
                  ))}
                </Select>
                {errors.vehicleUseType && <ErrorMessage>{errors.vehicleUseType}</ErrorMessage>}
              </FormField>

              <FormField name="vehicleType">
                <FormLabel>Tipo *</FormLabel>
                <Select
                  value={formData.vehicleType}
                  onChange={(e) => handleInputChange('vehicleType', e.target.value)}
                  $hasError={!!errors.vehicleType}
                >
                  <option value="">Seleccione el tipo</option>
                  {vehicleTypes.map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </Select>
                {errors.vehicleType && <ErrorMessage>{errors.vehicleType}</ErrorMessage>}
              </FormField>

              <FormField name="state">
                <FormLabel>Estado *</FormLabel>
                <Select
                  value={formData.state}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  $hasError={!!errors.state}
                >
                  <option value="">Seleccione un estado</option>
                  {states.map(state => (
                    <option key={state.id} value={state.id}>
                      {state.name}
                    </option>
                  ))}
                </Select>
                {errors.state && <ErrorMessage>{errors.state}</ErrorMessage>}
              </FormField>

              <FormField name="municipality">
                <FormLabel>Municipio *</FormLabel>
                <Select
                  value={formData.municipality}
                  onChange={(e) => handleInputChange('municipality', e.target.value)}
                  disabled={!formData.state}
                  $hasError={!!errors.municipality}
                >
                  <option value="">Seleccione un municipio</option>
                  {filteredMunicipalities.map(municipality => (
                    <option key={municipality.id} value={municipality.id}>
                      {municipality.name}
                    </option>
                  ))}
                </Select>
                {errors.municipality && <ErrorMessage>{errors.municipality}</ErrorMessage>}
              </FormField>

              <FormField name="brand">
                <FormLabel>Marca *</FormLabel>
                <Input
                  type="text"
                  placeholder="Toyota, Ford, Chevrolet..."
                  value={formData.brand}
                  onChange={(e) => handleInputChange('brand', e.target.value)}
                  $hasError={!!errors.brand}
                  $autoFilled={autoFilledFields.has('brand')}
                />
                {errors.brand && <ErrorMessage>{errors.brand}</ErrorMessage>}
                {autoFilledFields.has('brand') && (
                  <AutoFilledIndicator>
                    <SparklesIcon />
                    Completado automáticamente
                  </AutoFilledIndicator>
                )}
              </FormField>

              <FormField name="model">
                <FormLabel>Modelo *</FormLabel>
                <Input
                  type="text"
                  placeholder="Corolla, F-150, Aveo..."
                  value={formData.model}
                  onChange={(e) => handleInputChange('model', e.target.value)}
                  $hasError={!!errors.model}
                  $autoFilled={autoFilledFields.has('model')}
                />
                {errors.model && <ErrorMessage>{errors.model}</ErrorMessage>}
                {autoFilledFields.has('model') && (
                  <AutoFilledIndicator>
                    <SparklesIcon />
                    Completado automáticamente
                  </AutoFilledIndicator>
                )}
              </FormField>

              <FormField name="year">
                <FormLabel>Año *</FormLabel>
                <Input
                  type="number"
                  placeholder="2020"
                  min="1900"
                  max={new Date().getFullYear() + 1}
                  value={formData.year}
                  onChange={(e) => handleInputChange('year', parseInt(e.target.value) || new Date().getFullYear())}
                  $hasError={!!errors.year}
                  $autoFilled={autoFilledFields.has('year')}
                />
                {errors.year && <ErrorMessage>{errors.year}</ErrorMessage>}
                {autoFilledFields.has('year') && (
                  <AutoFilledIndicator>
                    <SparklesIcon />
                    Completado automáticamente
                  </AutoFilledIndicator>
                )}
              </FormField>

              <FormField name="color">
                <FormLabel>Color *</FormLabel>
                <Input
                  type="text"
                  placeholder="Blanco, Negro, Azul..."
                  value={formData.color}
                  onChange={(e) => handleInputChange('color', e.target.value)}
                  $hasError={!!errors.color}
                  $autoFilled={autoFilledFields.has('color')}
                />
                {errors.color && <ErrorMessage>{errors.color}</ErrorMessage>}
                {autoFilledFields.has('color') && (
                  <AutoFilledIndicator>
                    <SparklesIcon />
                    Completado automáticamente
                  </AutoFilledIndicator>
                )}
              </FormField>

              <FormField name="plateNumber">
                <FormLabel>Número de Placa *</FormLabel>
                <Input
                  type="text"
                  placeholder="ABC123"
                  value={formData.plateNumber}
                  onChange={(e) => handleInputChange('plateNumber', e.target.value.toUpperCase())}
                  $hasError={!!errors.plateNumber}
                  $autoFilled={autoFilledFields.has('plateNumber')}
                />
                {errors.plateNumber && <ErrorMessage>{errors.plateNumber}</ErrorMessage>}
                {autoFilledFields.has('plateNumber') && (
                  <AutoFilledIndicator>
                    <SparklesIcon />
                    Completado automáticamente
                  </AutoFilledIndicator>
                )}
              </FormField>

              <FormField name="chassisNumber">
                <FormLabel>Número de Chasis *</FormLabel>
                <Input
                  type="text"
                  placeholder="Ingrese el número de chasis"
                  value={formData.chassisNumber}
                  onChange={(e) => handleInputChange('chassisNumber', e.target.value.toUpperCase())}
                  $hasError={!!errors.chassisNumber}
                  $autoFilled={autoFilledFields.has('chassisNumber')}
                />
                {errors.chassisNumber && <ErrorMessage>{errors.chassisNumber}</ErrorMessage>}
                {autoFilledFields.has('chassisNumber') && (
                  <AutoFilledIndicator>
                    <SparklesIcon />
                    Completado automáticamente
                  </AutoFilledIndicator>
                )}
              </FormField>

              <FormField name="engineNumber">
                <FormLabel>Número de Motor *</FormLabel>
                <Input
                  type="text"
                  placeholder="Ingrese el número de motor"
                  value={formData.engineNumber}
                  onChange={(e) => handleInputChange('engineNumber', e.target.value.toUpperCase())}
                  $hasError={!!errors.engineNumber}
                  $autoFilled={autoFilledFields.has('engineNumber')}
                />
                {errors.engineNumber && <ErrorMessage>{errors.engineNumber}</ErrorMessage>}
                {autoFilledFields.has('engineNumber') && (
                  <AutoFilledIndicator>
                    <SparklesIcon />
                    Completado automáticamente
                  </AutoFilledIndicator>
                )}
              </FormField>
            </FormGrid>
          </Section>

          {/* Document Upload Section */}
          <Section>
            <SectionHeader>
              <SectionIcon>
                <DocumentTextIcon />
              </SectionIcon>
              <SectionTitle>Documentos</SectionTitle>
            </SectionHeader>

            <FormField name="documentFile">
              <FormLabel>Título o Carnet de Circulación</FormLabel>
              <FileUpload
                onFileSelect={handleFileSelect}
                selectedFile={selectedFile}
              />
              <FormDescription>
                Suba una copia del título de propiedad o carnet de circulación del vehículo (PDF, JPG, PNG - máx. 5MB)
              </FormDescription>
            </FormField>
          </Section>

          {/* Submit Button */}
          <SubmitSection>
            <SubmitButton type="submit">
              Enviar Registro
            </SubmitButton>
          </SubmitSection>
        </StyledForm>
      </Container>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleConfirm}
        data={formData}
        isLoading={isLoading}
      />

      {/* Loading Overlay */}
      <LoadingOverlay
        isVisible={isLoading}
        message="Registrando vehículo..."
      />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${props => props.theme.space[6]};
`;

const Header = styled.div`
  margin-bottom: ${props => props.theme.space[8]};
  text-align: center;
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.space[2]};
  transition: color 0.3s ease;
`;

const Subtitle = styled.p`
  font-size: ${props => props.theme.fontSizes.lg};
  color: ${props => props.theme.colors.textLight};
  margin: 0;
  transition: color 0.3s ease;
`;

const StyledForm = styled(Form.Root)`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[8]};
`;

const Section = styled.div`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  padding: ${props => props.theme.space[6]};
  box-shadow: ${props => props.theme.shadows.sm};
  border: 1px solid ${props => props.theme.colors.borderLight};
  transition: all 0.3s ease;
`;

const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[3]};
  margin-bottom: ${props => props.theme.space[6]};
  padding-bottom: ${props => props.theme.space[4]};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const SectionIcon = styled.div`
  width: 24px;
  height: 24px;
  color: ${props => props.theme.colors.primary};

  svg {
    width: 100%;
    height: 100%;
  }
`;

const SectionTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
  transition: color 0.3s ease;
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.space[4]};

  @media (min-width: 640px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
`;

const FormField = styled(Form.Field)`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[2]};
`;

const FormLabel = styled(Form.Label)`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;


const Input = styled.input<{ $hasError?: boolean; $autoFilled?: boolean }>`
  width: 100%;
  height: 42px;
  padding: 0 ${props => props.theme.space[3]};
  border: 1px solid ${props => {
    if (props.$hasError) return props.theme.colors.danger;
    if (props.$autoFilled) return props.theme.colors.success;
    return props.theme.colors.borderLight;
  }};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  transition: ${props => props.theme.transitions.default};
  background-color: ${props => {
    if (props.$autoFilled) {
      return props.theme.themeMode === 'dark'
        ? 'rgba(48, 164, 108, 0.1)'
        : 'rgba(48, 164, 108, 0.05)';
    }
    return props.theme.themeMode === 'dark' ? props.theme.colors.backgroundMuted : 'transparent';
  }};
  color: ${props => props.theme.colors.text};
  position: relative;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => `${props.theme.colors.primary}20`};
  }

  &::placeholder {
    color: ${props => props.theme.colors.textLight};
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundMuted};
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

const Select = styled.select<{ $hasError?: boolean; $autoFilled?: boolean }>`
  width: 100%;
  height: 42px;
  padding: 0 ${props => props.theme.space[3]};
  border: 1px solid ${props => {
    if (props.$hasError) return props.theme.colors.danger;
    if (props.$autoFilled) return props.theme.colors.success;
    return props.theme.colors.borderLight;
  }};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  transition: ${props => props.theme.transitions.default};
  background-color: ${props => {
    if (props.$autoFilled) {
      return props.theme.themeMode === 'dark'
        ? 'rgba(48, 164, 108, 0.1)'
        : 'rgba(48, 164, 108, 0.05)';
    }
    return props.theme.themeMode === 'dark' ? props.theme.colors.backgroundMuted : 'white';
  }};
  color: ${props => props.theme.colors.text};
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => `${props.theme.colors.primary}20`};
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundMuted};
    cursor: not-allowed;
    opacity: 0.6;
  }
`;



const FormDescription = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  margin: ${props => props.theme.space[1]} 0 0;
  transition: color 0.3s ease;
`;

const ErrorMessage = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.danger};
  margin-top: ${props => props.theme.space[1]};
  transition: color 0.3s ease;
`;

const AutoFilledIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[1]};
  font-size: ${props => props.theme.fontSizes.xs};
  color: ${props => props.theme.colors.success};
  margin-top: ${props => props.theme.space[1]};

  svg {
    width: 12px;
    height: 12px;
  }
`;

const SubmitSection = styled.div`
  display: flex;
  justify-content: center;
  padding-top: ${props => props.theme.space[4]};
`;

const SubmitButton = styled.button`
  padding: ${props => props.theme.space[4]} ${props => props.theme.space[8]};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;

  &:hover {
    background-color: ${props => props.theme.colors.primaryHover};
    transform: translateY(-1px);
    box-shadow: ${props => props.theme.shadows.md};
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

export default VehicleRegistration;
