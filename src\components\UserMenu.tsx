// src/components/UserMenu.tsx
import React from 'react';
import styled from 'styled-components';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import * as Avatar from '@radix-ui/react-avatar';
import {
  PersonIcon,
  GearIcon,
  ExitIcon,
  BellIcon,
  EnvelopeClosedIcon,
  CheckIcon  
} from '@radix-ui/react-icons';

interface UserMenuProps {
  userName: string;
  userEmail: string;
  userAvatar?: string;
  onLogout: () => void;
}

const UserMenu: React.FC<UserMenuProps> = ({
  userName,
  userEmail,
  userAvatar,
  onLogout
}) => {
  return (
    <UserMenuContainer>
      {/* <NotificationButton aria-label="Notifications">
        <BellIcon />
        <NotificationBadge>3</NotificationBadge>
      </NotificationButton>

      <MessageButton aria-label="Messages">
        <EnvelopeClosedIcon />
        <MessageBadge>5</MessageBadge>
      </MessageButton> */}

      <DropdownMenu.Root>
        <DropdownMenuTrigger aria-label="User menu">
          <AvatarContainer>
            <StyledAvatarRoot>
              <StyledAvatarImage
                src={userAvatar}
                alt={userName}
              />
              <StyledAvatarFallback delayMs={600}>
                <PersonIcon />
              </StyledAvatarFallback>
            </StyledAvatarRoot>
          </AvatarContainer>
        </DropdownMenuTrigger>

        <DropdownMenu.Portal>
          <DropdownMenuContent sideOffset={5} align="end">
            <UserInfo>
              <UserName>{userName}</UserName>
              <UserEmail>{userEmail}</UserEmail>
            </UserInfo>

            <DropdownMenu.Separator className="dropdown-separator" />

            <DropdownMenuItem>
              <MenuItemIcon>
                <PersonIcon />
              </MenuItemIcon>
              <span>Mi Perfil</span>
            </DropdownMenuItem>

            <DropdownMenuItem>
              <MenuItemIcon>
                <GearIcon />
              </MenuItemIcon>
              <span>Configuracion</span>
            </DropdownMenuItem>

            <DropdownMenuItem>
              <MenuItemIcon>
                <BellIcon />
              </MenuItemIcon>
              <span>Notificaciones</span>
            </DropdownMenuItem>

            {/* <DropdownMenu.Sub>
              <DropdownMenu.SubTrigger className="dropdown-sub-trigger">
                <MenuItemIcon>
                  <BellIcon />
                </MenuItemIcon>
                <span>Notifications</span>
              </DropdownMenu.SubTrigger>
              <DropdownMenu.Portal>
                <DropdownMenu.SubContent className="dropdown-sub-content">
                  <DropdownMenuItem>
                    <MenuItemIcon>
                      <CheckIcon />
                    </MenuItemIcon>
                    <span>All Notifications</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <MenuItemIcon>
                      <CheckIcon />
                    </MenuItemIcon>
                    <span>Email Notifications</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <MenuItemIcon>
                      <CheckIcon />
                    </MenuItemIcon>
                    <span>Push Notifications</span>
                  </DropdownMenuItem>
                </DropdownMenu.SubContent>
              </DropdownMenu.Portal>
            </DropdownMenu.Sub> */}

            <DropdownMenu.Separator className="dropdown-separator" />

            <DropdownMenuItem onClick={onLogout}>
              <MenuItemIcon>
                <ExitIcon />
              </MenuItemIcon>
              <span>Logout</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu.Portal>
      </DropdownMenu.Root>
    </UserMenuContainer>
  );
};

// Styled Components
const UserMenuContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[4]};
`;

const NotificationButton = styled.button`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: ${props => props.theme.radii.full};
  color: ${props => props.theme.colors.textLight};
  transition: ${props => props.theme.transitions.default};

  &:hover {
    background-color: ${props =>
      props.theme.themeMode === 'light'
        ? props.theme.colors.borderLight
        : 'rgba(255, 255, 255, 0.1)'
    };
    color: ${props => props.theme.colors.text};
  }
`;

const MessageButton = styled(NotificationButton)``;

const NotificationBadge = styled.span`
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: ${props => props.theme.radii.full};
  background-color: ${props => props.theme.colors.danger};
  color: white;
  font-size: ${props => props.theme.fontSizes.xs};
  font-weight: ${props => props.theme.fontWeights.bold};
  transition: background-color 0.3s ease;
`;

const MessageBadge = styled(NotificationBadge)`
  background-color: ${props => props.theme.colors.primary};
`;

const AvatarContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
`;

const StyledAvatarRoot = styled(Avatar.Root)`
  width: 36px;
  height: 36px;
  border-radius: ${props => props.theme.radii.full};
  overflow: hidden;
  background-color: ${props => props.theme.colors.borderLight};
  transition: background-color 0.3s ease;
`;

const StyledAvatarImage = styled(Avatar.Image)`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const StyledAvatarFallback = styled(Avatar.Fallback)`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  transition: background-color 0.3s ease;
`;

const DropdownMenuTrigger = styled(DropdownMenu.Trigger)`
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
`;

const DropdownMenuContent = styled(DropdownMenu.Content)`
  min-width: 220px;
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  padding: ${props => props.theme.space[2]};
  box-shadow: ${props => props.theme.shadows.lg};
  animation: fadeIn 200ms ease;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;

  .dropdown-separator {
    height: 1px;
    background-color: ${props => props.theme.colors.borderLight};
    margin: ${props => props.theme.space[2]} 0;
    transition: background-color 0.3s ease;
  }

  .dropdown-sub-trigger {
    display: flex;
    align-items: center;
    padding: ${props => props.theme.space[2]} ${props => props.theme.space[3]};
    border-radius: ${props => props.theme.radii.md};
    cursor: pointer;
    outline: none;
    transition: ${props => props.theme.transitions.default};

    &:hover {
      background-color: ${props =>
        props.theme.themeMode === 'light'
          ? props.theme.colors.borderLight
          : 'rgba(255, 255, 255, 0.1)'
      };
    }

    &[data-highlighted] {
      background-color: ${props => props.theme.colors.primary};
      color: white;
    }

    span {
      flex: 1;
    }
  }

  .dropdown-sub-content {
    min-width: 180px;
    background-color: ${props => props.theme.colors.white};
    border-radius: ${props => props.theme.radii.lg};
    padding: ${props => props.theme.space[2]};
    box-shadow: ${props => props.theme.shadows.lg};
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
`;

const UserInfo = styled.div`
  padding: ${props => props.theme.space[3]};
`;

const UserName = styled.div`
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.space[1]};
  transition: color 0.3s ease;
`;

const UserEmail = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const DropdownMenuItem = styled(DropdownMenu.Item)`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[3]};
  border-radius: ${props => props.theme.radii.md};
  cursor: pointer;
  outline: none;
  transition: ${props => props.theme.transitions.default};

  &:hover {
    background-color: ${props =>
      props.theme.themeMode === 'light'
        ? props.theme.colors.borderLight
        : 'rgba(255, 255, 255, 0.1)'
    };
  }

  &[data-highlighted] {
    background-color: ${props => props.theme.colors.primary};
    color: white;
  }
`;

const MenuItemIcon = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: ${props => props.theme.space[2]};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;

  ${DropdownMenuItem}:hover & {
    color: ${props => props.theme.colors.text};
  }

  ${DropdownMenuItem}[data-highlighted] & {
    color: white;
  }
`;

export default UserMenu;
