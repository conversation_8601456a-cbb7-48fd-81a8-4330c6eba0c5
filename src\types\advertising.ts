// src/types/advertising.ts

export interface AdvertisingFormData {
  contributor: {
    rif: string;
    nameOrBusinessName: string;
    accountNumber: string;
    phone: string;
    email: string;
    address: string;
    state: string;
    municipality: string;
  };
  registrationDetails: {
    exactAddress: string;
  };
  advertisingType: {
    type: string;
    description: string;
    dimensions: string;
    area: number;
    numberOfElements: number;
    duration: {
      start: string;
      end: string;
    };
    numberOfDays: number;
    numberOfPromoters: number;
    fraction: string;
  };
  declaration: {
    period: string;
    exchangeType: string;
    taxAmount: number;
  };
}

export interface AdvertisingEntry {
  id: string;
  type: string;
  description: string;
  area: number;
  numberOfElements: number;
  numberOfDays: number;
  fraction: string;
  exchangeType: string;
  taxAmount: number;
}

// Compatible interface for ConfirmationModal
export interface AdvertisingRegistrationData {
  rif: string;
  nameOrBusinessName: string;
  accountNumber: string;
  phone: string;
  email: string;
  address: string;
  state: string;
  municipality: string;
  exactAddress: string;
  period: string;
  advertisingEntries: AdvertisingEntry[];
  totalAmount: number;
}

export const ADVERTISING_TYPES = [
  { value: 'vallas', label: 'Vallas Publicitarias' },
  { value: 'pancartas', label: 'Pancartas' },
  { value: 'carteles', label: 'Carteles' },
  { value: 'luminosos', label: 'Anuncios Luminosos' },
  { value: 'digitales', label: 'Pantallas Digitales' },
  { value: 'vehiculares', label: 'Publicidad Vehicular' },
  { value: 'sonora', label: 'Publicidad Sonora' },
  { value: 'volantes', label: 'Volantes y Folletos' },
  { value: 'otros', label: 'Otros' }
];

export const EXCHANGE_TYPES = [
  { value: 'mmv_1', label: 'MMV Tipo 1' },
  { value: 'mmv_2', label: 'MMV Tipo 2' },
  { value: 'mmv_3', label: 'MMV Tipo 3' },
  { value: 'mmv_4', label: 'MMV Tipo 4' }
];

export const FRACTIONS = [
  { value: '1/1', label: '1/1' },
  { value: '1/2', label: '1/2' },
  { value: '1/3', label: '1/3' },
  { value: '1/4', label: '1/4' },
  { value: '1/5', label: '1/5' },
  { value: '1/6', label: '1/6' },
  { value: '1/8', label: '1/8' },
  { value: '1/10', label: '1/10' },
  { value: '1/12', label: '1/12' }
];

export const PERIODS = [
  { value: '2025', label: '2025' },
  { value: '2024', label: '2024' },
  { value: '2023', label: '2023' },
  { value: '2022', label: '2022' },
  { value: '2021', label: '2021' }
];

// Tax calculation rates (example rates)
export const TAX_RATES = {
  vallas: 0.05,
  pancartas: 0.03,
  carteles: 0.02,
  luminosos: 0.08,
  digitales: 0.10,
  vehiculares: 0.04,
  sonora: 0.06,
  volantes: 0.01,
  otros: 0.03
};
