// src/pages/apuestas-licitas/LegalBets.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import * as Toast from '@radix-ui/react-toast';
import { 
  UserIcon, 
  DocumentTextIcon, 
  CurrencyDollarIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import type { LegalBetFormData, BetEntry } from '../../types/legalBets';
import { DECLARATION_TYPES, MONTHS, TAX_RATES } from '../../types/legalBets';
import { BET_TYPES, getBetTypeById } from '../../data/betTypes';
import { STATES } from '../../data/states';
import { getMunicipalitiesByState } from '../../data/municipalities';
import LegalBetConfirmationModal from '../../components/LegalBetConfirmationModal';

const LegalBets: React.FC = () => {
  // Set page title
  useEffect(() => {
    document.title = 'Taxlite | Apuestas Lícitas';
  }, []);
  const [formData, setFormData] = useState<LegalBetFormData>({
    contributor: {
      rif: 'J-********-9',
      nameOrBusinessName: 'EMPRESA DE APUESTAS EJEMPLO C.A.',
      accountNumber: '**********',
      phone: '+58 ************',
      email: '<EMAIL>',
      address: 'Av. Principal, Centro Comercial Plaza, Local 15',
      state: 'Distrito Capital',
      municipality: 'Libertador'
    },
    declaration: {
      type: 'mensual',
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear()
    },
    betDetails: {
      betType: '',
      betNumber: '',
      betAmount: 0,
      taxRate: '',
      taxAmount: 0
    }
  });

  const [betEntries, setBetEntries] = useState<BetEntry[]>([]);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [municipalities, setMunicipalities] = useState<Array<{id: string, name: string}>>([]);

  // Load municipalities when state changes
  useEffect(() => {
    if (formData.contributor.state) {
      const stateMunicipalities = getMunicipalitiesByState(formData.contributor.state);
      setMunicipalities(stateMunicipalities);
    }
  }, [formData.contributor.state]);

  const handleInputChange = (section: keyof LegalBetFormData, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const calculateTaxAmount = (amount: number, taxRate: string): number => {
    const rate = parseFloat(taxRate.replace('%', '')) / 100;
    return amount * rate;
  };

  const handleAddBetEntry = () => {
    const { betType, betNumber, betAmount } = formData.betDetails;
    
    if (!betType || !betNumber || betAmount <= 0) {
      toast.error('Por favor complete todos los campos de la apuesta');
      return;
    }

    const taxRate = TAX_RATES[betType as keyof typeof TAX_RATES] || '0%';
    const taxAmount = calculateTaxAmount(betAmount, taxRate);
    const betTypeData = getBetTypeById(betType);

    const newEntry: BetEntry = {
      id: Date.now().toString(),
      betType,
      betTypeName: betTypeData?.name || betType,
      numberOfBets: parseInt(betNumber) || 1,
      betAmount,
      taxRate,
      taxAmount
    };

    setBetEntries(prev => [...prev, newEntry]);
    
    // Reset bet details form
    setFormData(prev => ({
      ...prev,
      betDetails: {
        betType: '',
        betNumber: '',
        betAmount: 0,
        taxRate: '',
        taxAmount: 0
      }
    }));

    toast.success('Apuesta agregada correctamente');
  };

  const handleRemoveBetEntry = (id: string) => {
    setBetEntries(prev => prev.filter(entry => entry.id !== id));
    toast.success('Apuesta eliminada');
  };

  const calculateTotalAmount = (): number => {
    return betEntries.reduce((total, entry) => total + entry.taxAmount, 0);
  };

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('es-VE', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const handleSubmit = () => {
    if (betEntries.length === 0) {
      toast.error('Debe agregar al menos una apuesta');
      return;
    }
    setShowConfirmModal(true);
  };

  const handleConfirmedSubmit = async () => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const submissionData = {
        ...formData,
        betEntries,
        totalAmount: calculateTotalAmount(),
        submittedAt: new Date().toISOString()
      };

      console.log('Datos de apuestas lícitas enviados:', submissionData);
      
      toast.success('Declaración de apuestas lícitas enviada exitosamente');
      setShowConfirmModal(false);
      
      // Reset form
      setBetEntries([]);
      setFormData(prev => ({
        ...prev,
        betDetails: {
          betType: '',
          betNumber: '',
          betAmount: 0,
          taxRate: '',
          taxAmount: 0
        }
      }));
      
    } catch (error) {
      console.error('Error al enviar declaración:', error);
      toast.error('Error al enviar la declaración. Intente nuevamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container>
      <Header>
        <Title>Apuestas Lícitas</Title>
        <Subtitle>Declaración correspondiente al impuesto sobre juego y apuestas lícitas</Subtitle>
      </Header>

      <FormContainer>
        {/* Contributor Information */}
        <FormSection>
          <SectionHeader>
            <SectionIcon>
              <UserIcon />
            </SectionIcon>
            <SectionTitle>Datos del Contribuyente</SectionTitle>
          </SectionHeader>
          <SectionContent>
            <FormGrid>
              <FormGroup>
                <Label>1. RIF</Label>
                <Input
                  type="text"
                  value={formData.contributor.rif}
                  readOnly
                  disabled
                />
              </FormGroup>
              <FormGroup $span={2}>
                <Label>2. Apellidos y Nombres / Razón Social</Label>
                <Input
                  type="text"
                  value={formData.contributor.nameOrBusinessName}
                  readOnly
                  disabled
                />
              </FormGroup>
              <FormGroup>
                <Label>3. N° de Cuenta</Label>
                <Input
                  type="text"
                  value={formData.contributor.accountNumber}
                  readOnly
                  disabled
                />
              </FormGroup>
              <FormGroup>
                <Label>4. Teléfonos</Label>
                <Input
                  type="text"
                  value={formData.contributor.phone}
                  readOnly
                  disabled
                />
              </FormGroup>
              <FormGroup>
                <Label>5. Correos</Label>
                <Input
                  type="email"
                  value={formData.contributor.email}
                  readOnly
                  disabled
                />
              </FormGroup>
              <FormGroup $span={2}>
                <Label>6. Dirección</Label>
                <Input
                  type="text"
                  value={formData.contributor.address}
                  readOnly
                  disabled
                />
              </FormGroup>
              <FormGroup>
                <Label>7. Estado</Label>
                <Input
                  type="text"
                  value={formData.contributor.state}
                  readOnly
                  disabled
                />
              </FormGroup>
              <FormGroup>
                <Label>8. Municipio</Label>
                <Input
                  type="text"
                  value={formData.contributor.municipality}
                  readOnly
                  disabled
                />
              </FormGroup>
            </FormGrid>
          </SectionContent>
        </FormSection>

        {/* Declaration Period */}
        <FormSection>
          <SectionHeader>
            <SectionIcon>
              <DocumentTextIcon />
            </SectionIcon>
            <SectionTitle>Periodo de la Declaración</SectionTitle>
          </SectionHeader>
          <SectionContent>
            <FormGrid>
              <FormGroup>
                <Label>Esquema de pago</Label>
                <Select
                  value={formData.declaration.type}
                  onChange={(e) => handleInputChange('declaration', 'type', e.target.value)}
                >
                  {DECLARATION_TYPES.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </Select>
              </FormGroup>
              <FormGroup>
                <Label>Año</Label>
                <Select
                  value={formData.declaration.year}
                  onChange={(e) => handleInputChange('declaration', 'year', parseInt(e.target.value))}
                >
                  {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - 5 + i).map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </Select>
              </FormGroup>
              <FormGroup>
                <Label>Mes</Label>
                <Select
                  value={formData.declaration.month}
                  onChange={(e) => handleInputChange('declaration', 'month', parseInt(e.target.value))}
                >
                  {MONTHS.map(month => (
                    <option key={month.value} value={month.value}>
                      {month.label}
                    </option>
                  ))}
                </Select>
              </FormGroup>
            </FormGrid>
          </SectionContent>
        </FormSection>

        {/* Bet Details */}
        <FormSection>
          <SectionHeader>
            <SectionIcon>
              <CurrencyDollarIcon />
            </SectionIcon>
            <SectionTitle>Detalles de la Apuesta</SectionTitle>
          </SectionHeader>
          <SectionContent>
            <FormGrid>
              <FormGroup $span={2}>
                <Label>9. Tipo de Apuesta</Label>
                <Select
                  value={formData.betDetails.betType}
                  onChange={(e) => handleInputChange('betDetails', 'betType', e.target.value)}
                >
                  <option value="">Seleccione un tipo de apuesta</option>
                  {BET_TYPES.map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </Select>
              </FormGroup>
              <FormGroup>
                <Label>10. N°. de Apuesta o ticket</Label>
                <Input
                  type="number"
                  value={formData.betDetails.betNumber}
                  onChange={(e) => handleInputChange('betDetails', 'betNumber', e.target.value)}
                  placeholder="Número de apuestas"
                />
              </FormGroup>
              <FormGroup>
                <Label>11. Monto Apostado</Label>
                <Input
                  type="number"
                  step="0.01"
                  value={formData.betDetails.betAmount || ''}
                  onChange={(e) => handleInputChange('betDetails', 'betAmount', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                />
              </FormGroup>
              <FormGroup>
                <Label>12. Alícuota</Label>
                <Input
                  type="text"
                  value={formData.betDetails.betType ? TAX_RATES[formData.betDetails.betType as keyof typeof TAX_RATES] || '0%' : ''}
                  readOnly
                  disabled
                />
              </FormGroup>
              <FormGroup>
                <Label>13. Monto impuesto (Bs.)</Label>
                <Input
                  type="text"
                  value={formData.betDetails.betType && formData.betDetails.betAmount > 0
                    ? formatCurrency(calculateTaxAmount(formData.betDetails.betAmount, TAX_RATES[formData.betDetails.betType as keyof typeof TAX_RATES] || '0%'))
                    : '0.00'
                  }
                  readOnly
                  disabled
                />
              </FormGroup>
            </FormGrid>

            <AddButtonContainer>
              <AddButton onClick={handleAddBetEntry}>
                <PlusIcon />
                Agregar Apuesta
              </AddButton>
            </AddButtonContainer>

            {/* Bet Entries Table */}
            {betEntries.length > 0 && (
              <BetTable>
                <TableHeader>
                  <TableRow>
                    <TableHeaderCell>Tipo de Apuesta</TableHeaderCell>
                    <TableHeaderCell>N° de Apuestas</TableHeaderCell>
                    <TableHeaderCell>Monto Apostado</TableHeaderCell>
                    <TableHeaderCell>Alícuota</TableHeaderCell>
                    <TableHeaderCell>Monto Impuesto (Bs.)</TableHeaderCell>
                    <TableHeaderCell>Acciones</TableHeaderCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {betEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>{entry.betTypeName}</TableCell>
                      <TableCell>{entry.numberOfBets}</TableCell>
                      <TableCell>{formatCurrency(entry.betAmount)}</TableCell>
                      <TableCell>{entry.taxRate}</TableCell>
                      <TableCell>
                        <TaxAmount>{formatCurrency(entry.taxAmount)}</TaxAmount>
                      </TableCell>
                      <TableCell>
                        <RemoveButton onClick={() => handleRemoveBetEntry(entry.id)}>
                          <TrashIcon />
                        </RemoveButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </BetTable>
            )}

            {betEntries.length > 0 && (
              <TotalSection>
                <TotalLabel>Total a pagar (Bs.):</TotalLabel>
                <TotalAmount>{formatCurrency(calculateTotalAmount())}</TotalAmount>
              </TotalSection>
            )}
          </SectionContent>
        </FormSection>

        {/* Submit Button */}
        <SubmitButtonContainer>
          <SubmitButton onClick={handleSubmit}>
            Enviar Registro
          </SubmitButton>
        </SubmitButtonContainer>
      </FormContainer>

      {/* Confirmation Modal */}
      <LegalBetConfirmationModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleConfirmedSubmit}
        data={formData}
        betEntries={betEntries}
        totalAmount={calculateTotalAmount()}
        isLoading={isLoading}
      />
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  min-height: 100vh;
  background-color: ${props => props.theme.colors.background};
  padding: ${props => props.theme.space[6]};
`;

const Header = styled.div`
  margin-bottom: ${props => props.theme.space[8]};
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.space[2]} 0;
`;

const Subtitle = styled.p`
  font-size: ${props => props.theme.fontSizes.lg};
  color: ${props => props.theme.colors.textLight};
  margin: 0;
`;

const FormContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[6]};
`;

const FormSection = styled.div`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  border: 1px solid ${props => props.theme.colors.borderLight};
  overflow: hidden;
`;

const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[3]};
  padding: ${props => props.theme.space[4]} ${props => props.theme.space[6]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const SectionIcon = styled.div`
  width: 20px;
  height: 20px;
  color: ${props => props.theme.colors.primary};

  svg {
    width: 100%;
    height: 100%;
  }
`;

const SectionTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const SectionContent = styled.div`
  padding: ${props => props.theme.space[6]};
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${props => props.theme.space[4]};

  @media (min-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }
`;

const FormGroup = styled.div<{ $span?: number }>`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[2]};

  ${props => props.$span && `
    grid-column: span ${props.$span};
  `}
`;

const Label = styled.label`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
`;

const Input = styled.input`
  padding: ${props => props.theme.space[3]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.text};
  background-color: ${props => props.theme.colors.white};
  transition: ${props => props.theme.transitions.default};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.textLight};
    cursor: not-allowed;
  }

  &::placeholder {
    color: ${props => props.theme.colors.textLight};
  }
`;

const Select = styled.select`
  padding: ${props => props.theme.space[3]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.text};
  background-color: ${props => props.theme.colors.white};
  transition: ${props => props.theme.transitions.default};
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.textLight};
    cursor: not-allowed;
  }
`;

const AddButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: ${props => props.theme.space[4]};
  margin-bottom: ${props => props.theme.space[4]};
`;

const AddButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 16px;
    height: 16px;
  }

  &:hover {
    background-color: ${props => props.theme.colors.primaryHover};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}30;
  }
`;

// Table styles
const BetTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: ${props => props.theme.space[4]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  overflow: hidden;
`;

const TableHeader = styled.thead`
  background-color: ${props => props.theme.colors.backgroundMuted};
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  &:not(:last-child) {
    border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  }

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted}50;
  }
`;

const TableHeaderCell = styled.th`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[2]};
  text-align: left;
  font-weight: ${props => props.theme.fontWeights.semibold};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const TableCell = styled.td`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[2]};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
  vertical-align: middle;
`;

const TaxAmount = styled.span`
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.primary};
`;

const RemoveButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: ${props => props.theme.colors.danger};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 16px;
    height: 16px;
  }

  &:hover {
    background-color: ${props => props.theme.colors.dangerHover || '#dc2626'};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px ${props => props.theme.colors.danger}30;
  }
`;

const TotalSection = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: ${props => props.theme.space[3]};
  margin-top: ${props => props.theme.space[4]};
  padding: ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  border: 1px solid ${props => props.theme.colors.borderLight};
`;

const TotalLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
`;

const TotalAmount = styled.span`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.primary};
  min-width: 120px;
  text-align: right;
`;

const SubmitButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: ${props => props.theme.space[6]};
`;

const SubmitButton = styled.button`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[8]};
  background-color: ${props => props.theme.colors.success};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};
  min-width: 200px;

  &:hover {
    background-color: ${props => props.theme.colors.successHover || '#16a34a'};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px ${props => props.theme.colors.success}30;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

export default LegalBets;
