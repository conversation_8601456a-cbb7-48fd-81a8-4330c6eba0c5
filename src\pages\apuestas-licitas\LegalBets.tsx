// src/pages/apuestas-licitas/LegalBets.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import * as Form from '@radix-ui/react-form';
import { 
  UserIcon, 
  DocumentTextIcon, 
  CurrencyDollarIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { useToast } from '../../components/ToastProvider';
import LoadingOverlay from '../../components/LoadingOverlay';
import type { LegalBetFormData, BetEntry } from '../../types/legalBets';
import { DECLARATION_TYPES, TAX_RATES } from '../../types/legalBets';
import { MONTHS } from '../../types/misc';
import { BET_TYPES, getBetTypeById } from '../../data/betTypes';
import { states } from '../../data/states';
import { municipalities } from '../../data/municipalities';
import LegalBetConfirmationModal from '../../components/LegalBetConfirmationModalSimple';

const LegalBets: React.FC = () => {
  const { showToast } = useToast();

  // Set page title
  useEffect(() => {
    document.title = 'Taxia | Apuestas Lícitas';
  }, []);

  // Form state
  const [formData, setFormData] = useState<LegalBetFormData>({
    contributor: {
      rif: 'J-********-9',
      nameOrBusinessName: 'EMPRESA DE APUESTAS EJEMPLO C.A.',
      accountNumber: '**********',
      phone: '+58 ************',
      email: '<EMAIL>',
      address: 'Av. Principal, Centro Comercial Plaza, Local 15',
      state: 'Distrito Capital',
      municipality: 'Libertador'
    },
    declaration: {
      type: 'mensual',
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear()
    },
    betDetails: {
      betType: '',
      betNumber: '',
      betAmount: 0,
      taxRate: '',
      taxAmount: 0
    }
  });

  // UI state
  const [betEntries, setBetEntries] = useState<BetEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [filteredMunicipalities, setFilteredMunicipalities] = useState(municipalities);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Filter municipalities when state changes
  useEffect(() => {
    if (formData.contributor.state) {
      const stateData = states.find(s => s.name === formData.contributor.state);
      if (stateData) {
        const filtered = municipalities.filter(m => m.stateId === stateData.id);
        setFilteredMunicipalities(filtered);

        // Reset municipality if it doesn't belong to the selected state
        if (formData.contributor.municipality && !filtered.find(m => m.name === formData.contributor.municipality)) {
          setFormData(prev => ({
            ...prev,
            contributor: { ...prev.contributor, municipality: '' }
          }));
        }
      }
    } else {
      setFilteredMunicipalities([]);
      setFormData(prev => ({
        ...prev,
        contributor: { ...prev.contributor, municipality: '' }
      }));
    }
  }, [formData.contributor.state, formData.contributor.municipality]);

  // Handle input changes
  const handleInputChange = (section: keyof LegalBetFormData, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));

    // Clear error when user starts typing
    const errorKey = `${section}.${field}`;
    if (errors[errorKey]) {
      setErrors(prev => ({ ...prev, [errorKey]: '' }));
    }
  };

  const calculateTaxAmount = (amount: number, taxRate: string): number => {
    const rate = parseFloat(taxRate.replace('%', '')) / 100;
    return amount * rate;
  };

  const handleAddBetEntry = () => {
    const { betType, betNumber, betAmount } = formData.betDetails;
    
    if (!betType || !betNumber || betAmount <= 0) {
      showToast('Error de validación', 'Por favor complete todos los campos de la apuesta', 'error');
      return;
    }

    const taxRate = TAX_RATES[betType as keyof typeof TAX_RATES] || '0%';
    const taxAmount = calculateTaxAmount(betAmount, taxRate);
    const betTypeData = getBetTypeById(betType);

    const newEntry: BetEntry = {
      id: Date.now().toString(),
      betType,
      betTypeName: betTypeData?.name || betType,
      numberOfBets: parseInt(betNumber) || 1,
      betAmount,
      taxRate,
      taxAmount
    };

    setBetEntries(prev => [...prev, newEntry]);
    
    // Reset bet details form
    setFormData(prev => ({
      ...prev,
      betDetails: {
        betType: '',
        betNumber: '',
        betAmount: 0,
        taxRate: '',
        taxAmount: 0
      }
    }));

    showToast('Éxito', 'Apuesta agregada correctamente', 'success');
  };

  const handleRemoveBetEntry = (id: string) => {
    setBetEntries(prev => prev.filter(entry => entry.id !== id));
    showToast('Éxito', 'Apuesta eliminada', 'success');
  };

  const calculateTotalAmount = (): number => {
    return betEntries.reduce((total, entry) => total + entry.taxAmount, 0);
  };

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('es-VE', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Reset form to initial state
  const resetForm = () => {
    setBetEntries([]);
    setFormData(prev => ({
      ...prev,
      betDetails: {
        betType: '',
        betNumber: '',
        betAmount: 0,
        taxRate: '',
        taxAmount: 0
      }
    }));
    setErrors({});
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (betEntries.length === 0) {
      newErrors.betEntries = 'Debe agregar al menos una apuesta';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      showToast('Error de validación', 'Por favor, corrija los errores en el formulario', 'error');
      return;
    }

    setShowConfirmModal(true);
  };

  // Handle confirmation
  const handleConfirm = async () => {
    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const submissionData = {
        ...formData,
        betEntries,
        totalAmount: calculateTotalAmount(),
        submittedAt: new Date().toISOString()
      };

      console.log('Datos de apuestas lícitas enviados:', submissionData);
      
      // 1. Close the confirmation modal
      setShowConfirmModal(false);

      // 2, 3, 4. Clear all form fields and reset state
      resetForm();

      // 5. Show success notification
      showToast(
        'Registro exitoso',
        'Declaración de apuestas lícitas enviada exitosamente',
        'success'
      );

    } catch (error) {
      console.error('Error al enviar declaración:', error);
      showToast('Error', 'Ha ocurrido un error inesperado', 'error');
    } finally {
      // 6. Hide loading state
      setIsLoading(false);
    }
  };

  return (
    <>
      <Container>
        <Header>
          <Title>Apuestas Lícitas</Title>
          <Subtitle>Declaración correspondiente al impuesto sobre juego y apuestas lícitas</Subtitle>
        </Header>

        <StyledForm onSubmit={handleSubmit}>
          {/* Contributor Information Section */}
          <Section>
            <SectionHeader>
              <SectionIcon>
                <UserIcon />
              </SectionIcon>
              <SectionTitle>Datos del Contribuyente</SectionTitle>
            </SectionHeader>

            <FormGrid>
              <FormField name="rif">
                <FormLabel>RIF</FormLabel>
                <Input
                  type="text"
                  value={formData.contributor.rif}
                  readOnly
                  disabled
                />
              </FormField>

              <FormField name="nameOrBusinessName" $span={2}>
                <FormLabel>Apellidos y Nombres / Razón Social</FormLabel>
                <Input
                  type="text"
                  value={formData.contributor.nameOrBusinessName}
                  readOnly
                  disabled
                />
              </FormField>

              <FormField name="accountNumber">
                <FormLabel>N° de Cuenta</FormLabel>
                <Input
                  type="text"
                  value={formData.contributor.accountNumber}
                  readOnly
                  disabled
                />
              </FormField>

              <FormField name="phone">
                <FormLabel>Teléfonos</FormLabel>
                <Input
                  type="text"
                  value={formData.contributor.phone}
                  readOnly
                  disabled
                />
              </FormField>

              <FormField name="email">
                <FormLabel>Correos</FormLabel>
                <Input
                  type="email"
                  value={formData.contributor.email}
                  readOnly
                  disabled
                />
              </FormField>

              <FormField name="address" $span={2}>
                <FormLabel>Dirección</FormLabel>
                <Input
                  type="text"
                  value={formData.contributor.address}
                  readOnly
                  disabled
                />
              </FormField>

              <FormField name="state">
                <FormLabel>Estado</FormLabel>
                <Input
                  type="text"
                  value={formData.contributor.state}
                  readOnly
                  disabled
                />
              </FormField>

              <FormField name="municipality">
                <FormLabel>Municipio</FormLabel>
                <Input
                  type="text"
                  value={formData.contributor.municipality}
                  readOnly
                  disabled
                />
              </FormField>
            </FormGrid>
          </Section>

          {/* Declaration Period Section */}
          <Section>
            <SectionHeader>
              <SectionIcon>
                <DocumentTextIcon />
              </SectionIcon>
              <SectionTitle>Periodo de la Declaración</SectionTitle>
            </SectionHeader>

            <FormGrid>
              <FormField name="declarationType">
                <FormLabel>Esquema de pago *</FormLabel>
                <Select
                  value={formData.declaration.type}
                  onChange={(e) => handleInputChange('declaration', 'type', e.target.value)}
                >
                  {DECLARATION_TYPES.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </Select>
              </FormField>

              <FormField name="year">
                <FormLabel>Año *</FormLabel>
                <Select
                  value={formData.declaration.year}
                  onChange={(e) => handleInputChange('declaration', 'year', parseInt(e.target.value))}
                >
                  {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - 5 + i).map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </Select>
              </FormField>

              <FormField name="month">
                <FormLabel>Mes *</FormLabel>
                <Select
                  value={formData.declaration.month}
                  onChange={(e) => handleInputChange('declaration', 'month', parseInt(e.target.value))}
                >
                  {MONTHS.map(month => (
                    <option key={month.value} value={month.value}>
                      {month.label}
                    </option>
                  ))}
                </Select>
              </FormField>
            </FormGrid>
          </Section>

          {/* Bet Details Section */}
          <Section>
            <SectionHeader>
              <SectionIcon>
                <CurrencyDollarIcon />
              </SectionIcon>
              <SectionTitle>Detalles de la Apuesta</SectionTitle>
            </SectionHeader>

            <FormGrid>
              <FormField name="betType" $span={2}>
                <FormLabel>Tipo de Apuesta *</FormLabel>
                <Select
                  value={formData.betDetails.betType}
                  onChange={(e) => handleInputChange('betDetails', 'betType', e.target.value)}
                >
                  <option value="">Seleccione un tipo de apuesta</option>
                  {BET_TYPES.map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </Select>
              </FormField>

              <FormField name="betNumber">
                <FormLabel>N°. de Apuesta o ticket *</FormLabel>
                <Input
                  type="number"
                  value={formData.betDetails.betNumber}
                  onChange={(e) => handleInputChange('betDetails', 'betNumber', e.target.value)}
                  placeholder="Número de apuestas"
                />
              </FormField>

              <FormField name="betAmount">
                <FormLabel>Monto Apostado *</FormLabel>
                <Input
                  type="number"
                  step="0.01"
                  value={formData.betDetails.betAmount || ''}
                  onChange={(e) => handleInputChange('betDetails', 'betAmount', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                />
              </FormField>

              <FormField name="taxRate">
                <FormLabel>Alícuota</FormLabel>
                <Input
                  type="text"
                  value={formData.betDetails.betType ? TAX_RATES[formData.betDetails.betType as keyof typeof TAX_RATES] || '0%' : ''}
                  readOnly
                  disabled
                />
              </FormField>

              <FormField name="taxAmount">
                <FormLabel>Monto impuesto (Bs.)</FormLabel>
                <Input
                  type="text"
                  value={formData.betDetails.betType && formData.betDetails.betAmount > 0
                    ? formatCurrency(calculateTaxAmount(formData.betDetails.betAmount, TAX_RATES[formData.betDetails.betType as keyof typeof TAX_RATES] || '0%'))
                    : '0.00'
                  }
                  readOnly
                  disabled
                />
              </FormField>
            </FormGrid>

            <AddButtonContainer>
              <AddButton type="button" onClick={handleAddBetEntry}>
                <PlusIcon />
                Agregar Apuesta
              </AddButton>
            </AddButtonContainer>

            {/* Bet Entries Table */}
            {betEntries.length > 0 && (
              <BetTable>
                <TableHeader>
                  <TableRow>
                    <TableHeaderCell>Tipo de Apuesta</TableHeaderCell>
                    <TableHeaderCell>N° de Apuestas</TableHeaderCell>
                    <TableHeaderCell>Monto Apostado</TableHeaderCell>
                    <TableHeaderCell>Alícuota</TableHeaderCell>
                    <TableHeaderCell>Monto Impuesto (Bs.)</TableHeaderCell>
                    <TableHeaderCell>Acciones</TableHeaderCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {betEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>{entry.betTypeName}</TableCell>
                      <TableCell>{entry.numberOfBets}</TableCell>
                      <TableCell>{formatCurrency(entry.betAmount)}</TableCell>
                      <TableCell>{entry.taxRate}</TableCell>
                      <TableCell>
                        <TaxAmount>{formatCurrency(entry.taxAmount)}</TaxAmount>
                      </TableCell>
                      <TableCell>
                        <RemoveButton type="button" onClick={() => handleRemoveBetEntry(entry.id)}>
                          <TrashIcon />
                        </RemoveButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </BetTable>
            )}

            {betEntries.length > 0 && (
              <TotalSection>
                <TotalLabel>Total a pagar (Bs.):</TotalLabel>
                <TotalAmount>{formatCurrency(calculateTotalAmount())}</TotalAmount>
              </TotalSection>
            )}

            {errors.betEntries && <ErrorMessage>{errors.betEntries}</ErrorMessage>}
          </Section>

          {/* Submit Button */}
          <SubmitSection>
            <SubmitButton type="submit">
              Enviar Registro
            </SubmitButton>
          </SubmitSection>
        </StyledForm>
      </Container>

      {/* Confirmation Modal */}
      <LegalBetConfirmationModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleConfirm}
        data={{
          rif: formData.contributor.rif,
          nameOrBusinessName: formData.contributor.nameOrBusinessName,
          accountNumber: formData.contributor.accountNumber,
          phone: formData.contributor.phone,
          email: formData.contributor.email,
          address: formData.contributor.address,
          state: formData.contributor.state,
          municipality: formData.contributor.municipality,
          declarationType: formData.declaration.type,
          month: formData.declaration.month,
          year: formData.declaration.year,
          betEntries: betEntries,
          totalAmount: calculateTotalAmount()
        }}
        isLoading={isLoading}
      />

      {/* Loading Overlay */}
      <LoadingOverlay
        isVisible={isLoading}
        message="Enviando declaración..."
      />
    </>
  );
};

// Styled Components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${props => props.theme.space[6]};
`;

const Header = styled.div`
  margin-bottom: ${props => props.theme.space[8]};
  text-align: center;
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text};
  margin: 0 0 ${props => props.theme.space[2]};
  transition: color 0.3s ease;
`;

const Subtitle = styled.p`
  font-size: ${props => props.theme.fontSizes.lg};
  color: ${props => props.theme.colors.textLight};
  margin: 0;
  transition: color 0.3s ease;
`;

const StyledForm = styled(Form.Root)`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[8]};
`;

const Section = styled.div`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  padding: ${props => props.theme.space[6]};
  box-shadow: ${props => props.theme.shadows.sm};
  border: 1px solid ${props => props.theme.colors.borderLight};
  transition: all 0.3s ease;
`;

const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[3]};
  margin-bottom: ${props => props.theme.space[6]};
  padding-bottom: ${props => props.theme.space[4]};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const SectionIcon = styled.div`
  width: 24px;
  height: 24px;
  color: ${props => props.theme.colors.primary};

  svg {
    width: 100%;
    height: 100%;
  }
`;

const SectionTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
  transition: color 0.3s ease;
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.space[4]};

  @media (min-width: 640px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
`;

const FormField = styled(Form.Field)<{ $span?: number }>`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[2]};

  ${props => props.$span && `
    @media (min-width: 640px) {
      grid-column: span ${Math.min(props.$span, 2)};
    }
    @media (min-width: 1024px) {
      grid-column: span ${props.$span};
    }
  `}
`;

const FormLabel = styled(Form.Label)`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const Input = styled.input<{ $hasError?: boolean }>`
  width: 100%;
  height: 42px;
  padding: 0 ${props => props.theme.space[3]};
  border: 1px solid ${props => {
    if (props.$hasError) return props.theme.colors.danger;
    return props.theme.colors.borderLight;
  }};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  transition: ${props => props.theme.transitions.default};
  background-color: ${props =>
    props.theme.themeMode === 'dark' ? props.theme.colors.backgroundMuted : 'transparent'
  };
  color: ${props => props.theme.colors.text};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => `${props.theme.colors.primary}20`};
  }

  &::placeholder {
    color: ${props => props.theme.colors.textLight};
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundMuted};
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

const Select = styled.select<{ $hasError?: boolean }>`
  width: 100%;
  height: 42px;
  padding: 0 ${props => props.theme.space[3]};
  border: 1px solid ${props => {
    if (props.$hasError) return props.theme.colors.danger;
    return props.theme.colors.borderLight;
  }};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  transition: ${props => props.theme.transitions.default};
  background-color: ${props =>
    props.theme.themeMode === 'dark' ? props.theme.colors.backgroundMuted : 'white'
  };
  color: ${props => props.theme.colors.text};
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => `${props.theme.colors.primary}20`};
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundMuted};
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

const ErrorMessage = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.danger};
  margin-top: ${props => props.theme.space[1]};
  transition: color 0.3s ease;
`;

const AddButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: ${props => props.theme.space[4]};
  margin-bottom: ${props => props.theme.space[4]};
`;

const AddButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 16px;
    height: 16px;
  }

  &:hover {
    background-color: ${props => props.theme.colors.primaryHover};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}30;
  }
`;

// Table styles
const BetTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: ${props => props.theme.space[4]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  overflow: hidden;
`;

const TableHeader = styled.thead`
  background-color: ${props => props.theme.colors.backgroundMuted};
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  &:not(:last-child) {
    border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  }

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted}50;
  }
`;

const TableHeaderCell = styled.th`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[2]};
  text-align: left;
  font-weight: ${props => props.theme.fontWeights.semibold};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const TableCell = styled.td`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[2]};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
  vertical-align: middle;
`;

const TaxAmount = styled.span`
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.primary};
`;

const RemoveButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: ${props => props.theme.colors.danger};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 16px;
    height: 16px;
  }

  &:hover {
    background-color: ${props => props.theme.colors.dangerHover || '#dc2626'};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px ${props => props.theme.colors.danger}30;
  }
`;

const TotalSection = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: ${props => props.theme.space[3]};
  margin-top: ${props => props.theme.space[4]};
  padding: ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  border: 1px solid ${props => props.theme.colors.borderLight};
`;

const TotalLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
`;

const TotalAmount = styled.span`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.primary};
  min-width: 120px;
  text-align: right;
`;

const SubmitSection = styled.div`
  display: flex;
  justify-content: center;
  padding-top: ${props => props.theme.space[4]};
`;

const SubmitButton = styled.button`
  padding: ${props => props.theme.space[4]} ${props => props.theme.space[8]};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;

  &:hover {
    background-color: ${props => props.theme.colors.primaryHover};
    transform: translateY(-1px);
    box-shadow: ${props => props.theme.shadows.md};
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

export default LegalBets;
