// src/styles/ToastStyles.ts
import styled from 'styled-components';
import * as Toast from '@radix-ui/react-toast';
import { theme } from './theme';

export const ToastViewport = styled(Toast.Viewport)`
  position: fixed;
  bottom: ${theme.space[6]};
  right: ${theme.space[6]};
  display: flex;
  flex-direction: column;
  gap: ${theme.space[3]};
  width: 360px;
  max-width: 100vw;
  margin: 0;
  list-style: none;
  z-index: ${theme.zIndices.toast};
  outline: none;
`;

export const ToastRoot = styled(Toast.Root)`
  background-color: ${theme.colors.white};
  border-radius: ${theme.radii.lg};
  box-shadow: ${theme.shadows.lg};
  padding: ${theme.space[4]};
  display: grid;
  grid-template-areas: 'title action' 'description action';
  grid-template-columns: auto max-content;
  column-gap: ${theme.space[3]};
  align-items: center;
  border-left: 4px solid ${props => 
    props.variant === 'success' ? theme.colors.success :
    props.variant === 'error' ? theme.colors.danger :
    props.variant === 'warning' ? theme.colors.warning :
    theme.colors.primary
  };
  
  &[data-state='open'] {
    animation: slideIn 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  &[data-state='closed'] {
    animation: slideOut 150ms ease-out;
  }
  
  &[data-swipe='move'] {
    transform: translateX(var(--radix-toast-swipe-move-x));
  }
  
  &[data-swipe='cancel'] {
    transform: translateX(0);
    transition: transform 200ms ease-out;
  }
  
  &[data-swipe='end'] {
    animation: swipeOut 100ms ease-out;
  }
  
  @keyframes slideIn {
    from {
      transform: translateX(calc(100% + ${theme.space[6]}));
    }
    to {
      transform: translateX(0);
    }
  }
  
  @keyframes slideOut {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(100% + ${theme.space[6]}));
    }
  }
  
  @keyframes swipeOut {
    from {
      transform: translateX(var(--radix-toast-swipe-end-x));
    }
    to {
      transform: translateX(calc(100% + ${theme.space[6]}));
    }
  }
`;

export const ToastTitle = styled(Toast.Title)`
  grid-area: title;
  font-size: ${theme.fontSizes.md};
  font-weight: ${theme.fontWeights.semibold};
  color: ${theme.colors.text};
  margin-bottom: ${theme.space[1]};
`;

export const ToastDescription = styled(Toast.Description)`
  grid-area: description;
  font-size: ${theme.fontSizes.sm};
  color: ${theme.colors.textLight};
`;

export const ToastAction = styled(Toast.Action)`
  grid-area: action;
`;

export const ToastClose = styled(Toast.Close)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: ${theme.colors.textLight};
  background: transparent;
  border: none;
  border-radius: ${theme.radii.full};
  cursor: pointer;
  transition: ${theme.transitions.default};
  
  &:hover {
    background-color: ${theme.colors.borderLight};
    color: ${theme.colors.text};
  }
`;
