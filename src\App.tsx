import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { GlobalStyles } from './styles/GlobalStyles';
import { ThemeProvider } from './contexts/ThemeContext';
import { ToastProvider } from './components/ToastProvider';
import Layout from './layouts/Layout';
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import NotFound from './pages/NotFound';
import VehicleRegistration from './pages/registros/VehicleRegistration';
import EconomicActivities from './pages/actividades-economicas/EconomicActivities';

function App() {
  return (
    <ThemeProvider>
      <GlobalStyles />
      <ToastProvider>
        <BrowserRouter>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={<Layout />}>
              <Route index element={<Dashboard />} />

              {/* Registros y submenús */}
              <Route path="registros" element={<div>Página de Registros</div>} />
              <Route path="registros/actividades-economicas" element={<EconomicActivities />} />
              <Route path="registros/inmuebles-urbanos" element={<div>Registros - Inmuebles Urbanos</div>} />
              <Route path="registros/vehiculos" element={<VehicleRegistration />} />
              <Route path="registros/publicidad-propaganda" element={<div>Registros - Publicidad y Propaganda</div>} />
              <Route path="registros/espectaculos-publicos" element={<div>Registros - Espectáculos públicos</div>} />
              <Route path="registros/apuestas-licitas" element={<div>Registros - Apuestas lícitas</div>} />
              <Route path="registros/tasas" element={<div>Registros - Tasas</div>} />

              {/* Procesos tributarios y submenús */}
              <Route path="procesos-tributarios" element={<div>Página de Procesos Tributarios</div>} />
              <Route path="procesos-tributarios/rebajas-servicios" element={<div>Procesos Tributarios - Rebajas de Servicios</div>} />

              {/* Agentes de retenciones y submenús */}
              <Route path="agentes-retenciones" element={<div>Página de Agentes de Retenciones</div>} />
              <Route path="agentes-retenciones/retenciones-iae" element={<div>Agentes de Retenciones - Retenciones IAE</div>} />

              {/* Consultas y submenús */}
              <Route path="consultas" element={<div>Página de Consultas</div>} />
              <Route path="consultas/estado-cuenta" element={<div>Consultas - Estado de Cuenta</div>} />

              {/* Pagos */}
              <Route path="pagos" element={<div>Página de Pagos</div>} />

              {/* Base legal y submenús */}
              <Route path="base-legal" element={<div>Página de Base Legal</div>} />
              <Route path="base-legal/actividades-economicas" element={<div>Base Legal - Actividades Económicas</div>} />
              <Route path="base-legal/inmuebles-urbanos" element={<div>Base Legal - Inmuebles Urbanos</div>} />
              <Route path="base-legal/aseo-urbano" element={<div>Base Legal - Aseo Urbano</div>} />
              <Route path="base-legal/gas-domestico" element={<div>Base Legal - Gas Doméstico</div>} />
              <Route path="base-legal/vehiculos" element={<div>Base Legal - Vehículos</div>} />
              <Route path="base-legal/licores" element={<div>Base Legal - Licores</div>} />
              <Route path="base-legal/publicidad-propaganda" element={<div>Base Legal - Publicidad y Propaganda</div>} />
              <Route path="base-legal/espectaculos-publicos" element={<div>Base Legal - Espectáculos públicos</div>} />
              <Route path="base-legal/apuestas-licitas" element={<div>Base Legal - Apuestas Lícitas</div>} />
              <Route path="base-legal/tasas-administrativas" element={<div>Base Legal - Tasas Administrativas</div>} />

              {/* Autogestión de servicios municipales */}
              <Route path="autogestion" element={<div>Página de Autogestión de Servicios Municipales</div>} />
            </Route>
            <Route path="/404" element={<NotFound />} />
            <Route path="*" element={<Navigate to="/404" replace />} />
          </Routes>
        </BrowserRouter>
      </ToastProvider>
    </ThemeProvider>
  );
}

export default App;
