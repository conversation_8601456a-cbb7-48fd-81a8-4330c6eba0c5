// src/pages/Dashboard.tsx
import React from 'react';
import styled from 'styled-components';
import {
  BarChartIcon,
  PersonIcon,
  FileTextIcon,
  EnvelopeClosedIcon
} from '@radix-ui/react-icons';

const Dashboard: React.FC = () => {
  return (
    <DashboardContainer>
      <PageHeader>
        <PageTitle>Bienvenido a Taxia</PageTitle>
        <PageDescription>Sistema de gestión tributaria municipal</PageDescription>
      </PageHeader>

      <StatsGrid>
        <StatCard>
          <StatIcon $bgColor="primary">
            <PersonIcon />
          </StatIcon>
          <StatContent>
            <StatValue>2,543</StatValue>
            <StatLabel>Total Users</StatLabel>
          </StatContent>
          <StatChange $positive={true}>+12.5%</StatChange>
        </StatCard>

        <StatCard>
          <StatIcon $bgColor="success">
            <FileTextIcon />
          </StatIcon>
          <StatContent>
            <StatValue>1,259</StatValue>
            <StatLabel>Total Content</StatLabel>
          </StatContent>
          <StatChange $positive={true}>+8.2%</StatChange>
        </StatCard>

        <StatCard>
          <StatIcon $bgColor="info">
            <EnvelopeClosedIcon />
          </StatIcon>
          <StatContent>
            <StatValue>854</StatValue>
            <StatLabel>New Messages</StatLabel>
          </StatContent>
          <StatChange $positive={false}>-3.1%</StatChange>
        </StatCard>

        <StatCard>
          <StatIcon $bgColor="warning">
            <BarChartIcon />
          </StatIcon>
          <StatContent>
            <StatValue>$12,875</StatValue>
            <StatLabel>Total Revenue</StatLabel>
          </StatContent>
          <StatChange $positive={true}>+18.3%</StatChange>
        </StatCard>
      </StatsGrid>

      <SectionTitle>Recent Activity</SectionTitle>

      <GridContainer>
        <GridColumn>
          <Card>
            <CardHeader>
              <CardTitle>Latest Users</CardTitle>
              <CardAction>View All</CardAction>
            </CardHeader>
            <CardContent>
              <UserList>
                {[1, 2, 3, 4, 5].map(user => (
                  <UserItem key={user}>
                    <UserAvatar>
                      <PersonIcon />
                    </UserAvatar>
                    <UserInfo>
                      <UserName>User {user}</UserName>
                      <UserEmail>user{user}@example.com</UserEmail>
                    </UserInfo>
                    <UserDate>2h ago</UserDate>
                  </UserItem>
                ))}
              </UserList>
            </CardContent>
          </Card>
        </GridColumn>

        <GridColumn>
          <Card>
            <CardHeader>
              <CardTitle>Recent Content</CardTitle>
              <CardAction>View All</CardAction>
            </CardHeader>
            <CardContent>
              <ContentList>
                {[1, 2, 3, 4, 5].map(content => (
                  <ContentItem key={content}>
                    <ContentIcon>
                      <FileTextIcon />
                    </ContentIcon>
                    <ContentInfo>
                      <ContentTitle>Content Title {content}</ContentTitle>
                      <ContentMeta>Updated 3h ago</ContentMeta>
                    </ContentInfo>
                  </ContentItem>
                ))}
              </ContentList>
            </CardContent>
          </Card>
        </GridColumn>
      </GridContainer>
    </DashboardContainer>
  );
};

// Styled Components
const DashboardContainer = styled.div`
  width: 100%;
`;

const PageHeader = styled.div`
  margin-bottom: ${props => props.theme.space[6]};
`;

const PageTitle = styled.h1`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.space[2]};
  transition: color 0.3s ease;
`;

const PageDescription = styled.p`
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: ${props => props.theme.space[4]};
  margin-bottom: ${props => props.theme.space[6]};
`;

const StatCard = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
`;

const StatIcon = styled.div<{ $bgColor: string }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: ${props => props.theme.radii.md};
  background-color: ${props => props.theme.colors[props.$bgColor as keyof typeof props.theme.colors]};
  color: white;
  margin-right: ${props => props.theme.space[4]};
  transition: background-color 0.3s ease;
`;

const StatContent = styled.div`
  flex: 1;
`;

const StatValue = styled.div`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.space[1]};
  transition: color 0.3s ease;
`;

const StatLabel = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const StatChange = styled.div<{ $positive: boolean }>`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.$positive ? props.theme.colors.success : props.theme.colors.danger};
  transition: color 0.3s ease;
`;

const SectionTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.space[4]};
  transition: color 0.3s ease;
`;

const GridContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: ${props => props.theme.space[4]};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const GridColumn = styled.div``;

const Card = styled.div`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  overflow: hidden;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
`;

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.space[4]};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  transition: border-color 0.3s ease;
`;

const CardTitle = styled.h3`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const CardAction = styled.button`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.primary};
  font-weight: ${props => props.theme.fontWeights.medium};
  transition: color 0.3s ease;

  &:hover {
    text-decoration: underline;
  }
`;

const CardContent = styled.div`
  padding: ${props => props.theme.space[4]};
`;

const UserList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[3]};
`;

const UserItem = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.space[2]} 0;
`;

const UserAvatar = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: ${props => props.theme.radii.full};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  margin-right: ${props => props.theme.space[3]};
  transition: background-color 0.3s ease;
`;

const UserInfo = styled.div`
  flex: 1;
`;

const UserName = styled.div`
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.space[1]};
  transition: color 0.3s ease;
`;

const UserEmail = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const UserDate = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const ContentList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[3]};
`;

const ContentItem = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.space[2]} 0;
`;

const ContentIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: ${props => props.theme.radii.md};
  background-color: ${props => props.theme.colors.borderLight};
  color: ${props => props.theme.colors.text};
  margin-right: ${props => props.theme.space[3]};
  transition: background-color 0.3s ease, color 0.3s ease;
`;

const ContentInfo = styled.div`
  flex: 1;
`;

const ContentTitle = styled.div`
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.space[1]};
  transition: color 0.3s ease;
`;

const ContentMeta = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

export default Dashboard;
