// src/styles/darkTheme.ts
import { theme } from './theme';

export const darkTheme = {
  ...theme,
  colors: {
    ...theme.colors,
    // Radix UI colors - Indigo palette (dark mode)
    primary: '#5B7CFA',       // Indigo9 from Radix Colors (dark)
    primaryHover: '#6E8CFF',  // Indigo8 from Radix Colors (dark)
    primaryLight: '#1F2D5C',  // Indigo3 from Radix Colors (dark)
    secondary: '#8F8F8F',     // Gray9 from Radix Colors (dark)
    secondaryHover: '#A0A0A0', // Gray8 from Radix Colors (dark)
    success: '#3DD68C',       // Grass9 from Radix Colors (dark)
    danger: '#FF6369',        // Red9 from Radix Colors (dark)
    warning: '#FF8B3E',       // Amber9 from Radix Colors (dark)
    info: '#3AA5FF',          // Blue9 from Radix Colors (dark)

    // Background colors
    background: '#161618',    // Dark background from Radix UI
    backgroundDark: '#0F0F10', // Darker background
    backgroundSidebar: '#1A1A1C', // Sidebar background (dark)
    backgroundMuted: '#232326', // Muted background (dark)

    // Text colors
    text: '#EDEDEF',          // Main text color (dark)
    textLight: '#A09FA6',     // Secondary text color (dark)
    textMuted: '#7E7D86',     // Muted text color (dark)

    // Border colors
    border: '#313135',        // Border color (dark)
    borderLight: '#232326',   // Light border color (dark)

    // Other colors
    white: '#1A1A1C',         // "White" in dark mode is dark
    shadow: 'rgba(0, 0, 0, 0.3)',
    focus: 'rgba(91, 124, 250, 0.35)', // Focus ring color (Indigo9 dark)
  },
  shadows: {
    ...theme.shadows,
    sm: '0 1px 2px rgba(0, 0, 0, 0.2)',
    md: '0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2)',
    lg: '0 10px 30px -10px rgba(0, 0, 0, 0.4), 0 5px 15px -5px rgba(0, 0, 0, 0.3)',
    xl: '0 20px 40px -15px rgba(0, 0, 0, 0.5)',
    focus: '0 0 0 2px rgba(91, 124, 250, 0.35)', // Focus ring shadow (Indigo9 dark)
    sidebar: '1px 0 3px rgba(0, 0, 0, 0.2)',    // Sidebar shadow (dark)
  },
};

export type DarkTheme = typeof darkTheme;
