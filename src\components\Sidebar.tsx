// src/components/Sidebar.tsx
import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../contexts/ThemeContext';
import SidebarMenu from './SidebarMenu';

interface SidebarProps {
  isCollapsed: boolean;
  isMobile: boolean;
  isOverlayVisible: boolean;
  closeSidebar: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  isMobile,
  isOverlayVisible,
  closeSidebar
}) => {
  const { themeMode } = useTheme();

  // Choose logo based on theme
  const logoSrc = themeMode === 'dark'
    ? '/img/Logo-Taxia-horizontal-dark.png'
    : '/img/Logo-Taxia-horizontal.png';

  return (
    <>
      {/* Mobile Overlay */}
      {isOverlayVisible && (
        <MobileOverlay
          onClick={closeSidebar}
          aria-label="Close sidebar"
        />
      )}

      <SidebarContainer
        $isCollapsed={isCollapsed}
        $isMobile={isMobile}
      >
        <SidebarHeader>
          <LogoContainer>
            {/* Logo image that changes based on theme */}
            <LogoImage
              src={logoSrc}
              style={{ width: "100px", height: "auto" }}
              alt="Taxia Logo"
            />
            {!isCollapsed && <Logo>Taxia</Logo>}
          </LogoContainer>

        </SidebarHeader>

        <SidebarContent>
          <SidebarMenu isCollapsed={isCollapsed} />
        </SidebarContent>

        <SidebarFooter $isCollapsed={isCollapsed}>
          <FooterText>
            {!isCollapsed && 'Taxia v1.0'}
          </FooterText>
        </SidebarFooter>
      </SidebarContainer>
    </>
  );
};

// Styled Components
const MobileOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: ${props => props.theme.zIndices.sidebarOverlay};
  cursor: pointer;
  animation: fadeIn 0.2s ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
`;

const SidebarContainer = styled.aside<{ $isCollapsed: boolean; $isMobile: boolean }>`
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  width: ${props => props.$isCollapsed ? props.theme.sizes.sidebarCollapsedWidth : props.theme.sizes.sidebarWidth};
  background-color: ${props => props.theme.colors.backgroundSidebar};
  border-right: 1px solid ${props => props.theme.colors.border};
  display: flex;
  flex-direction: column;
  transition: all ${props => props.theme.transitions.default};
  z-index: ${props => props.theme.zIndices.docked};
  box-shadow: ${props => props.theme.shadows.sidebar};
  overflow-x: hidden;
  overflow-y: auto;

  /* Mobile responsive behavior */
  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    z-index: ${props => props.theme.zIndices.modal};
    transform: translateX(${props => props.$isCollapsed ? '-100%' : '0'});
    width: ${props => props.theme.sizes.sidebarWidth};
    box-shadow: ${props => props.$isCollapsed ? 'none' : props.theme.shadows.lg};
  }
`;

const SidebarHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[4]};
  height: ${props => props.theme.sizes.topbarHeight};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  transition: border-color 0.3s ease;
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
`;

const LogoImage = styled.img`
  width: 30px;
  height: 30px;
  border-radius: ${props => props.theme.radii.sm};
`;

const Logo = styled.div`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  white-space: nowrap;
  transition: color 0.3s ease;
`;

const SidebarContent = styled.div`
  flex: 1;
  overflow-y: auto;

  /* Radix UI style scrollbar for sidebar content */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.textMuted};
    border: 2px solid ${props => props.theme.colors.backgroundMuted};
    border-radius: ${props => props.theme.radii.full};
  }
`;

const SidebarFooter = styled.div<{ $isCollapsed: boolean }>`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[4]};
  border-top: 1px solid ${props => props.theme.colors.border};
  text-align: ${props => props.$isCollapsed ? 'center' : 'left'};
  transition: border-color 0.3s ease;
`;

const FooterText = styled.div`
  font-size: ${props => props.theme.fontSizes.xs};
  color: ${props => props.theme.colors.textMuted};
  transition: color 0.3s ease;
`;

export default Sidebar;
