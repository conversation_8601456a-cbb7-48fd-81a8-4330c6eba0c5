// src/components/ConfirmationModal.tsx
import React from 'react';
import styled from 'styled-components';
import * as Dialog from '@radix-ui/react-dialog';
import { XMarkIcon } from '@heroicons/react/24/outline';
import type { VehicleRegistrationData } from '../services/vehicleApi';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  data: VehicleRegistrationData;
  isLoading?: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  data,
  isLoading = false
}) => {
  const formatFieldName = (key: string): string => {
    const fieldNames: Record<string, string> = {
      documentType: 'Tipo de Documento',
      rifNumber: 'Número de RIF',
      nameOrBusinessName: 'Nombre o Razón Social',
      contributorSince: 'Contribuyente desde',
      vehicleUseType: 'Tipo de uso',
      vehicleType: 'Tipo',
      state: 'Estado',
      municipality: 'Municipio',
      brand: 'Marca',
      model: 'Modelo',
      year: 'Año',
      color: 'Color',
      plateNumber: 'Número de Placa',
      chassisNumber: 'Número de Chasis',
      engineNumber: 'Número de Motor',
      documentFile: 'Documento'
    };
    return fieldNames[key] || key;
  };

  const formatValue = (key: string, value: any): string => {
    if (key === 'documentFile' && value instanceof File) {
      return value.name;
    }
    return value?.toString() || '';
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Overlay />
        <Content>
          <Header>
            <Title>Confirmar Registro de Vehículo</Title>
            <CloseButton onClick={onClose}>
              <XMarkIcon />
            </CloseButton>
          </Header>

          <Body>
            <Description>
              Por favor, revise los datos antes de confirmar el registro:
            </Description>

            <DataSection>
              <SectionTitle>Información del Propietario</SectionTitle>
              <DataGrid>
                {['documentType', 'rifNumber', 'nameOrBusinessName', 'contributorSince'].map((key) => (
                  <DataItem key={key}>
                    <DataLabel>{formatFieldName(key)}:</DataLabel>
                    <DataValue>{formatValue(key, data[key as keyof VehicleRegistrationData])}</DataValue>
                  </DataItem>
                ))}
              </DataGrid>
            </DataSection>

            <DataSection>
              <SectionTitle>Información del Vehículo</SectionTitle>
              <DataGrid>
                {['vehicleUseType', 'vehicleType', 'state', 'municipality', 'brand', 'model', 'year', 'color', 'plateNumber', 'chassisNumber', 'engineNumber'].map((key) => (
                  <DataItem key={key}>
                    <DataLabel>{formatFieldName(key)}:</DataLabel>
                    <DataValue>{formatValue(key, data[key as keyof VehicleRegistrationData])}</DataValue>
                  </DataItem>
                ))}
              </DataGrid>
            </DataSection>

            {data.documentFile && (
              <DataSection>
                <SectionTitle>Documento</SectionTitle>
                <DataItem>
                  <DataLabel>Archivo:</DataLabel>
                  <DataValue>{data.documentFile.name}</DataValue>
                </DataItem>
              </DataSection>
            )}
          </Body>

          <Footer>
            <CancelButton onClick={onClose} disabled={isLoading}>
              Cancelar
            </CancelButton>
            <ConfirmButton onClick={onConfirm} disabled={isLoading}>
              {isLoading ? 'Procesando...' : 'Confirmar Registro'}
            </ConfirmButton>
          </Footer>
        </Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

// Styled Components
const Overlay = styled(Dialog.Overlay)`
  background-color: rgba(0, 0, 0, 0.7);
  position: fixed;
  inset: 0;
  z-index: 1000;
`;

const Content = styled(Dialog.Content)`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  box-shadow: ${props => props.theme.shadows.xl};
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 600px;
  max-height: 85vh;
  overflow-y: auto;
  z-index: 1001;
  transition: background-color 0.3s ease;
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.space[6]};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const Title = styled(Dialog.Title)`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
  transition: color 0.3s ease;
`;

const CloseButton = styled.button`
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: ${props => props.theme.colors.textLight};
  cursor: pointer;
  border-radius: ${props => props.theme.radii.md};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.text};
  }

  svg {
    width: 20px;
    height: 20px;
  }
`;

const Body = styled.div`
  padding: ${props => props.theme.space[6]};
`;

const Description = styled.p`
  margin: 0 0 ${props => props.theme.space[6]};
  color: ${props => props.theme.colors.textLight};
  font-size: ${props => props.theme.fontSizes.md};
  transition: color 0.3s ease;
`;

const DataSection = styled.div`
  margin-bottom: ${props => props.theme.space[6]};

  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h3`
  margin: 0 0 ${props => props.theme.space[4]};
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const DataGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.space[3]};

  @media (min-width: 640px) {
    grid-template-columns: 1fr 1fr;
  }
`;

const DataItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[1]};
`;

const DataLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const DataValue = styled.span`
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.text};
  word-break: break-word;
  transition: color 0.3s ease;
`;

const Footer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${props => props.theme.space[3]};
  padding: ${props => props.theme.space[6]};
  border-top: 1px solid ${props => props.theme.colors.borderLight};
`;

const Button = styled.button`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[6]};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  color: ${props => props.theme.colors.textLight};
  border-color: ${props => props.theme.colors.borderLight};

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.text};
  }
`;

const ConfirmButton = styled(Button)`
  background-color: ${props => props.theme.colors.primary};
  color: white;

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.primaryHover};
  }
`;

export default ConfirmationModal;
