// src/services/vehicleApi.ts
import axios, { AxiosError } from 'axios';

// Get API base URL from environment variables
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// Vehicle registration data interface
export type VehicleRegistrationData = {
  // Owner Information
  documentType: string;
  rifNumber: string;
  nameOrBusinessName: string;
  contributorSince: string;

  // Vehicle Information
  vehicleUseType: string;
  vehicleType: string;
  state: string;
  municipality: string;
  brand: string;
  model: string;
  year: number;
  color: string;
  plateNumber: string;
  chassisNumber: string;
  engineNumber: string;

  // Document Upload
  documentFile?: File;
}

// API Response interface
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Error response interface
export interface ApiError {
  message: string;
  status?: number;
  isConnectionError?: boolean;
}

// Vehicle registration API service
export class VehicleApiService {
  /**
   * Register a new vehicle
   * @param data Vehicle registration data
   * @returns Promise with API response
   */
  static async registerVehicle(data: VehicleRegistrationData): Promise<ApiResponse> {
    try {
      // Create FormData for file upload
      const formData = new FormData();

      // Append all form fields
      Object.entries(data).forEach(([key, value]) => {
        if (key === 'documentFile' && value instanceof File) {
          formData.append('documentFile', value);
        } else if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      // Make API request
      const response = await apiClient.post('/v1/registration/vehicle', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return {
        success: true,
        data: response.data,
        message: 'Vehículo registrado exitosamente',
      };
    } catch (error) {
      return this.handleApiError(error);
    }
  }

  /**
   * Handle API errors and return standardized error response
   * @param error The error object from axios
   * @returns Standardized error response
   */
  private static handleApiError(error: unknown): ApiResponse {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;

      // Connection error (network issues, server down, etc.)
      if (!axiosError.response) {
        return {
          success: false,
          error: 'Error de conexión. Verifique su conexión a internet e intente nuevamente.',
          message: 'Error de conexión',
        };
      }

      // Server responded with error status
      const { status, data } = axiosError.response;
      const errorMessage = (data as any)?.message ||
                          (data as any)?.error ||
                          `Error del servidor (${status})`;

      return {
        success: false,
        error: errorMessage,
        message: 'Error en el servidor',
      };
    }

    // Unknown error
    return {
      success: false,
      error: 'Ha ocurrido un error inesperado. Intente nuevamente.',
      message: 'Error desconocido',
    };
  }

  /**
   * Validate file before upload
   * @param file File to validate
   * @returns Validation result
   */
  static validateFile(file: File): { isValid: boolean; error?: string } {
    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'El archivo no debe superar los 5MB',
      };
    }

    // Check file type (only PDF, JPG, PNG)
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Solo se permiten archivos PDF, JPG o PNG',
      };
    }

    return { isValid: true };
  }
}

export default VehicleApiService;
