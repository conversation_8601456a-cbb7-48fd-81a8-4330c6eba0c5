// src/types/economicActivity.ts

export interface EconomicActivity {
  number: string;
  code: string;
  descri: string;
  alicuota: string;
  mmv: number;
}

export interface ContributorInfo {
  nameOrBusinessName: string;
  licenseNumber: string;
  rif: string;
  email: string;
  legalResponsible: string;
  localPhone: string;
  cellPhone: string;
  address: string;
}

export interface Declaration {
  type: 'definitiva' | 'sustitutiva' | 'anticipada';
  year: number;
  month: number;
}

export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
}

export interface EconomicActivityFormData {
  contributor: ContributorInfo;
  declaration: Declaration;
  location: Location | null;
  selectedActivity: EconomicActivity | null;
  ivaDeclaration: File | null;
  ivaRelationDeclaration: File | null;
}

export const DECLARATION_TYPES = [
  { value: 'definitiva', label: 'Definitiva' },
  { value: 'sustitutiva', label: 'Sustitutiva' },
  // { value: 'anticipada', label: 'Anticipada' }
];

// Generate years from current year back to 10 years
export const getAvailableYears = (): Array<{ value: number; label: string }> => {
  const currentYear = new Date().getFullYear();
  const years = [];
  
  for (let year = currentYear; year >= currentYear - 10; year--) {
    years.push({ value: year, label: year.toString() });
  }
  
  return years;
};
