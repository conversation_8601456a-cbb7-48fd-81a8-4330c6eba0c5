// src/types/economicActivity.ts

export interface EconomicActivity {
  number: string;
  code: string;
  descri: string;
  alicuota: string;
  mmv: number;
}

export interface ContributorInfo {
  nameOrBusinessName: string;
  licenseNumber: string;
  rif: string;
  email: string;
  legalResponsible: string;
  localPhone: string;
  cellPhone: string;
  address: string;
}

export interface Declaration {
  type: 'definitiva' | 'sustitutiva' | 'anticipada';
  year: number;
  month: number;
}

export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
}

export interface EconomicActivityFormData {
  contributor: ContributorInfo;
  declaration: Declaration;
  location: Location | null;
  selectedActivity: EconomicActivity | null;
  ivaDeclaration: File | null;
  ivaRelationDeclaration: File | null;
}

export const MONTHS = [
  { value: 1, label: 'Enero' },
  { value: 2, label: 'Febrero' },
  { value: 3, label: 'Marzo' },
  { value: 4, label: 'Abril' },
  { value: 5, label: 'Mayo' },
  { value: 6, label: '<PERSON><PERSON>' },
  { value: 7, label: 'Julio' },
  { value: 8, label: 'Agosto' },
  { value: 9, label: 'Septiembre' },
  { value: 10, label: 'Octubre' },
  { value: 11, label: 'Noviembre' },
  { value: 12, label: 'Diciembre' }
];

export const DECLARATION_TYPES = [
  { value: 'definitiva', label: 'Definitiva' },
  { value: 'sustitutiva', label: 'Sustitutiva' },
  // { value: 'anticipada', label: 'Anticipada' }
];

// Generate years from current year back to 10 years
export const getAvailableYears = (): Array<{ value: number; label: string }> => {
  const currentYear = new Date().getFullYear();
  const years = [];
  
  for (let year = currentYear; year >= currentYear - 10; year--) {
    years.push({ value: year, label: year.toString() });
  }
  
  return years;
};
