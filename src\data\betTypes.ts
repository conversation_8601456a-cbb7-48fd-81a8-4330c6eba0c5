// src/data/betTypes.ts

export interface BetType {
  id: string;
  name: string;
}

export const BET_TYPES: BetType[] = [
  { id: 'loterias', name: 'Loterías' },
  { id: 'quinielas_pronosticos', name: 'Quinielas y Pronósticos Deportivos' },
  { id: 'apuestas_casinos', name: 'Apuestas en Casinos' },
  { id: 'apuestas_sorteos_suerte', name: 'Apuestas en Juegos de Sorteos y Suerte Administrados' },
  { id: 'apuestas_habilidad', name: 'Apuestas en Juegos de Habilidad y Competencias' },
  { id: 'apuestas_plataformas', name: 'Apuestas en Plataformas en Línea' },
  { id: 'bingo_otros', name: 'Bingo y Otros Juegos de Sorteo' }
];

export const getBetTypeById = (id: string): BetType | undefined => {
  return BET_TYPES.find(type => type.id === id);
};

export const getBetTypeByName = (name: string): BetType | undefined => {
  return BET_TYPES.find(type => type.name === name);
};
