// src/components/EconomicActivityConfirmationModal.tsx
import React from 'react';
import styled from 'styled-components';
import * as Dialog from '@radix-ui/react-dialog';
import {
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  UserIcon,
  DocumentTextIcon,
  MapPinIcon,
  BriefcaseIcon,
  PaperClipIcon
} from '@heroicons/react/24/outline';
import type { EconomicActivityFormData } from '../types/economicActivity';
import { MONTHS, DECLARATION_TYPES } from '../types/economicActivity';

interface ActivityWithAmount {
  code: string;
  description: string;
  montoBase: number;
  alicuota: string;
  impuesto: number;
}
import LoadingOverlay from './LoadingOverlay';

interface EconomicActivityConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  data: EconomicActivityFormData;
  activities: ActivityWithAmount[];
  totalDeclared: number;
  isLoading: boolean;
}

const EconomicActivityConfirmationModal: React.FC<EconomicActivityConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  data,
  activities,
  totalDeclared,
  isLoading
}) => {
  const getMonthName = (monthNumber: number) => {
    const month = MONTHS.find(m => m.value === monthNumber);
    return month ? month.label : monthNumber.toString();
  };

  const getDeclarationTypeName = (type: string) => {
    const declarationType = DECLARATION_TYPES.find(dt => dt.value === type);
    return declarationType ? declarationType.label : type;
  };

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('es-VE', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <DialogOverlay />
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Declaración de Actividades Económicas</DialogTitle>
            <DialogClose onClick={onClose} disabled={isLoading}>
              <XMarkIcon />
            </DialogClose>
          </DialogHeader>

          <DialogBody>
            <WarningSection>
              <WarningIcon>
                <ExclamationTriangleIcon />
              </WarningIcon>
              <WarningText>
                Por favor revise cuidadosamente la información antes de enviar la declaración.
                Una vez enviada, no podrá ser modificada.
              </WarningText>
            </WarningSection>

            <ConfirmationSections>
              {/* Contributor Information */}
              <ConfirmationSection>
                <SectionHeader>
                  <SectionIcon>
                    <UserIcon />
                  </SectionIcon>
                  <SectionTitle>Información del Contribuyente</SectionTitle>
                </SectionHeader>
                <SectionContent>
                  <DataRow>
                    <DataLabel>Nombre o razón social:</DataLabel>
                    <DataValue>{data.contributor.nameOrBusinessName}</DataValue>
                  </DataRow>
                  <DataRow>
                    <DataLabel>RIF:</DataLabel>
                    <DataValue>{data.contributor.rif}</DataValue>
                  </DataRow>
                  <DataRow>
                    <DataLabel>Número de licencia:</DataLabel>
                    <DataValue>{data.contributor.licenseNumber}</DataValue>
                  </DataRow>
                  <DataRow>
                    <DataLabel>Email:</DataLabel>
                    <DataValue>{data.contributor.email}</DataValue>
                  </DataRow>
                </SectionContent>
              </ConfirmationSection>

              {/* Declaration Information */}
              <ConfirmationSection>
                <SectionHeader>
                  <SectionIcon>
                    <DocumentTextIcon />
                  </SectionIcon>
                  <SectionTitle>Declaración</SectionTitle>
                </SectionHeader>
                <SectionContent>
                  <DataRow>
                    <DataLabel>Tipo de declaración:</DataLabel>
                    <DataValue>{getDeclarationTypeName(data.declaration.type)}</DataValue>
                  </DataRow>
                  <DataRow>
                    <DataLabel>Periodo:</DataLabel>
                    <DataValue>{getMonthName(data.declaration.month)} {data.declaration.year}</DataValue>
                  </DataRow>
                </SectionContent>
              </ConfirmationSection>

              {/* Location Information */}
              <ConfirmationSection>
                <SectionHeader>
                  <SectionIcon>
                    <MapPinIcon />
                  </SectionIcon>
                  <SectionTitle>Ubicación</SectionTitle>
                </SectionHeader>
                <SectionContent>
                  {data.location ? (
                    <>
                      <DataRow>
                        <DataLabel>Latitud:</DataLabel>
                        <DataValue>{data.location.latitude.toFixed(6)}</DataValue>
                      </DataRow>
                      <DataRow>
                        <DataLabel>Longitud:</DataLabel>
                        <DataValue>{data.location.longitude.toFixed(6)}</DataValue>
                      </DataRow>
                      {data.location.address && (
                        <DataRow>
                          <DataLabel>Dirección:</DataLabel>
                          <DataValue>{data.location.address}</DataValue>
                        </DataRow>
                      )}
                    </>
                  ) : (
                    <DataRow>
                      <DataValue>No se ha seleccionado ubicación</DataValue>
                    </DataRow>
                  )}
                </SectionContent>
              </ConfirmationSection>

              {/* Economic Activities Information */}
              <ConfirmationSection>
                <SectionHeader>
                  <SectionIcon>
                    <BriefcaseIcon />
                  </SectionIcon>
                  <SectionTitle>Actividades Económicas</SectionTitle>
                </SectionHeader>
                <SectionContent>
                  {activities.length > 0 ? (
                    <>
                      <ActivitiesTable>
                        <TableHeader>
                          <TableRow>
                            <TableHeaderCell>Código</TableHeaderCell>
                            <TableHeaderCell>Descripción</TableHeaderCell>
                            <TableHeaderCell>Monto Base</TableHeaderCell>
                            <TableHeaderCell>Alícuota</TableHeaderCell>
                            <TableHeaderCell>Impuesto</TableHeaderCell>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {activities.map((activity, index) => (
                            <TableRow key={index}>
                              <TableCell>{activity.code}</TableCell>
                              <TableCell>{activity.description}</TableCell>
                              <TableCell>{formatCurrency(activity.montoBase)}</TableCell>
                              <TableCell>{activity.alicuota}</TableCell>
                              <TableCellAmount>
                                <TaxAmount>{formatCurrency(activity.impuesto)}</TaxAmount>
                              </TableCellAmount>
                            </TableRow>
                          ))}
                        </TableBody>
                      </ActivitiesTable>

                      <TotalSection>
                        <TotalLabel>Total declarado:</TotalLabel>
                        <TotalAmount>{formatCurrency(totalDeclared)}</TotalAmount>
                      </TotalSection>
                    </>
                  ) : (
                    <DataRow>
                      <DataValue>No se han registrado actividades económicas con montos</DataValue>
                    </DataRow>
                  )}
                </SectionContent>
              </ConfirmationSection>

              {/* Documents Information */}
              <ConfirmationSection>
                <SectionHeader>
                  <SectionIcon>
                    <PaperClipIcon />
                  </SectionIcon>
                  <SectionTitle>Documentos Adjuntos</SectionTitle>
                </SectionHeader>
                <SectionContent>
                  <DataRow>
                    <DataLabel>Declaración de IVA del periodo:</DataLabel>
                    <DataValue>
                      {data.ivaDeclaration ? (
                        <FileInfo>
                          📄 {data.ivaDeclaration.name} ({(data.ivaDeclaration.size / 1024 / 1024).toFixed(2)} MB)
                        </FileInfo>
                      ) : (
                        <MissingFile>No adjuntado</MissingFile>
                      )}
                    </DataValue>
                  </DataRow>
                  <DataRow>
                    <DataLabel>Declaración de IVA relación de ingreso:</DataLabel>
                    <DataValue>
                      {data.ivaRelationDeclaration ? (
                        <FileInfo>
                          📄 {data.ivaRelationDeclaration.name} ({(data.ivaRelationDeclaration.size / 1024 / 1024).toFixed(2)} MB)
                        </FileInfo>
                      ) : (
                        <OptionalFile>No adjuntado (Opcional)</OptionalFile>
                      )}
                    </DataValue>
                  </DataRow>
                </SectionContent>
              </ConfirmationSection>
            </ConfirmationSections>
          </DialogBody>

          <DialogFooter>
            <CancelButton onClick={onClose} disabled={isLoading}>
              Cancelar
            </CancelButton>
            <ConfirmButton onClick={onConfirm} disabled={isLoading}>
              <CheckIcon />
              Confirmar y Enviar
            </ConfirmButton>
          </DialogFooter>

          {/* Loading Overlay */}
          {isLoading && (
            <LoadingOverlay
              isVisible={isLoading}
              message="Enviando declaración..."
            />
          )}
        </DialogContent>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

// Styled Components
const DialogOverlay = styled(Dialog.Overlay)`
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  inset: 0;
  z-index: 50;
`;

const DialogContent = styled(Dialog.Content)`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  box-shadow: ${props => props.theme.shadows.xl};
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 800px;
  max-height: 85vh;
  padding: 0;
  z-index: 51;
  display: flex;
  flex-direction: column;
`;

const DialogHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.space[6]};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const DialogTitle = styled(Dialog.Title)`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const DialogClose = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: ${props => props.theme.radii.md};
  border: none;
  background-color: transparent;
  color: ${props => props.theme.colors.textLight};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 20px;
    height: 20px;
  }

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.text};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const DialogBody = styled.div`
  flex: 1;
  overflow: auto;
  padding: ${props => props.theme.space[6]};
`;

const WarningSection = styled.div`
  display: flex;
  align-items: flex-start;
  gap: ${props => props.theme.space[3]};
  padding: ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.warning}15;
  border: 1px solid ${props => props.theme.colors.warning}30;
  border-radius: ${props => props.theme.radii.md};
  margin-bottom: ${props => props.theme.space[6]};
`;

const WarningIcon = styled.div`
  width: 20px;
  height: 20px;
  color: ${props => props.theme.colors.warning};
  flex-shrink: 0;

  svg {
    width: 100%;
    height: 100%;
  }
`;

const WarningText = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
  margin: 0;
  line-height: 1.5;
`;

const ConfirmationSections = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[4]};
`;

const ConfirmationSection = styled.div`
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  overflow: hidden;
`;

const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const SectionIcon = styled.div`
  width: 16px;
  height: 16px;
  color: ${props => props.theme.colors.primary};

  svg {
    width: 100%;
    height: 100%;
  }
`;

const SectionTitle = styled.h3`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const SectionContent = styled.div`
  padding: ${props => props.theme.space[4]};
`;

const DataRow = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[1]};
  margin-bottom: ${props => props.theme.space[3]};

  &:last-child {
    margin-bottom: 0;
  }

  @media (min-width: 640px) {
    flex-direction: row;
    align-items: flex-start;
  }
`;

const DataLabel = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.textLight};
  min-width: 140px;

  @media (min-width: 640px) {
    flex-shrink: 0;
  }
`;

const DataValue = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
  word-break: break-word;
`;

const FileInfo = styled.span`
  color: ${props => props.theme.colors.success};
  font-weight: ${props => props.theme.fontWeights.medium};
`;

const MissingFile = styled.span`
  color: ${props => props.theme.colors.danger};
  font-style: italic;
`;

const OptionalFile = styled.span`
  color: ${props => props.theme.colors.textLight};
  font-style: italic;
`;

const DialogFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${props => props.theme.space[3]};
  padding: ${props => props.theme.space[6]};
  border-top: 1px solid ${props => props.theme.colors.borderLight};
`;

const CancelButton = styled.button`
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[4]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  background-color: transparent;
  color: ${props => props.theme.colors.text};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.backgroundMuted};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ConfirmButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[4]};
  border: 1px solid ${props => props.theme.colors.primary};
  border-radius: ${props => props.theme.radii.md};
  background-color: ${props => props.theme.colors.primary};
  color: white;
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 16px;
    height: 16px;
  }

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.primaryHover};
    border-color: ${props => props.theme.colors.primaryHover};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

// Table styles
const ActivitiesTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: ${props => props.theme.space[4]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  overflow: hidden;
`;

const TableHeader = styled.thead`
  background-color: ${props => props.theme.colors.backgroundMuted};
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  &:not(:last-child) {
    border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  }
`;

const TableHeaderCell = styled.th`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[2]};
  text-align: left;
  font-weight: ${props => props.theme.fontWeights.semibold};
  font-size: ${props => props.theme.fontSizes.xs};
  color: ${props => props.theme.colors.text};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const TableCell = styled.td`
  padding: ${props => props.theme.space[2]};
  font-size: ${props => props.theme.fontSizes.xs};
  color: ${props => props.theme.colors.text};
  vertical-align: middle;
`;

const TableCellAmount = styled.td`
  padding: ${props => props.theme.space[2]};
  font-size: ${props => props.theme.fontSizes.xs};
  color: ${props => props.theme.colors.text};
  vertical-align: middle;
  text-align: right;
`;

const TaxAmount = styled.span`
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.primary};
`;

const TotalSection = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: ${props => props.theme.space[3]};
  padding: ${props => props.theme.space[3]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  border: 1px solid ${props => props.theme.colors.borderLight};
`;

const TotalLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
`;

const TotalAmount = styled.span`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.primary};
  min-width: 100px;
  text-align: right;
`;

export default EconomicActivityConfirmationModal;
