// src/components/Topbar.tsx
import React from 'react';
import styled from 'styled-components';
import { MagnifyingGlassIcon, HamburgerMenuIcon, Cross2Icon } from '@radix-ui/react-icons';
import { useTheme } from '../contexts/ThemeContext';
import UserMenu from './UserMenu';
import ThemeToggle from './ThemeToggle';

interface TopbarProps {
  title: string;
  isCollapsed: boolean;
  toggleSidebar: () => void;
  onLogout: () => void;
}

const Topbar: React.FC<TopbarProps> = ({
  // title,
  isCollapsed,
  toggleSidebar,
  onLogout
}) => {
  const [isSearchActive, setIsSearchActive] = React.useState(false);
  const { themeMode } = useTheme();

  // Choose logo based on theme
  const logoSrc = themeMode === 'dark'
    ? '/img/Logo-Taxia-horizontal-dark.png'
    : '/img/Logo-Taxia-horizontal.png';

  return (
    <TopbarContainer>
      <TopbarLeft>
        <MenuButton
          onClick={toggleSidebar}
          data-menu-button
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          <HamburgerMenuIcon />
        </MenuButton>

        {/* Logo image that changes based on theme */}
        <LogoContainer>
          <LogoImage src={logoSrc} style={{ width: "100px", height: "auto" }} alt="Taxia Logo" />
        </LogoContainer>

        {/* <Title>{title}</Title> */}
      </TopbarLeft>

      <TopbarRight>
        {isSearchActive ? (
          <ActiveSearchContainer>
            <SearchInput
              type="text"
              placeholder="Buscar..."
              aria-label="Search"
              autoFocus
            />
            <CloseSearchButton onClick={() => setIsSearchActive(false)}>
              <Cross2Icon />
            </CloseSearchButton>
          </ActiveSearchContainer>
        ) : (
          <SearchContainer>
            <SearchButton onClick={() => setIsSearchActive(true)}>
              <MagnifyingGlassIcon />
            </SearchButton>
          </SearchContainer>
        )}

        <ThemeToggle />
        <UserMenu
          userName="Admin User"
          userEmail="<EMAIL>"
          onLogout={onLogout}
        />
      </TopbarRight>
    </TopbarContainer>
  );
};

// Styled Components
const TopbarContainer = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: ${props => props.theme.sizes.topbarHeight};
  padding: 0 ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.white};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: ${props => props.theme.zIndices.sticky};
  box-shadow: ${props => props.theme.shadows.sm};
`;

const TopbarLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[3]};
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  margin-right: ${props => props.theme.space[3]};
`;

const LogoImage = styled.img`
  width: 30px;
  height: 30px;
  border-radius: ${props => props.theme.radii.sm};
`;

const MenuButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: ${props => props.theme.radii.md};
  color: ${props => props.theme.colors.textLight};
  transition: ${props => props.theme.transitions.default};

  &:hover {
    background-color: ${props =>
      props.theme.themeMode === 'light'
        ? 'rgba(0, 0, 0, 0.05)'
        : 'rgba(255, 255, 255, 0.05)'
    };
    color: ${props => props.theme.colors.text};
  }

  &:active {
    transform: scale(0.95);
  }

  & > svg {
    width: 16px;
    height: 16px;
  }
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
`;

const TopbarRight = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[3]};
`;

const SearchContainer = styled.div`
  position: relative;

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    display: none;
  }
`;

const SearchButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: ${props => props.theme.radii.md};
  color: ${props => props.theme.colors.textLight};
  transition: ${props => props.theme.transitions.default};

  &:hover {
    background-color: ${props =>
      props.theme.themeMode === 'light'
        ? 'rgba(0, 0, 0, 0.05)'
        : 'rgba(255, 255, 255, 0.05)'
    };
    color: ${props => props.theme.colors.text};
  }

  & > svg {
    width: 16px;
    height: 16px;
  }
`;

const ActiveSearchContainer = styled.div`
  position: relative;
  width: 240px;
  display: flex;
  align-items: center;

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    display: none;
  }
`;

const SearchInput = styled.input`
  width: 100%;
  height: 32px;
  padding: 0 ${props => props.theme.space[7]} 0 ${props => props.theme.space[3]};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.radii.md};
  background-color: ${props => props.theme.colors.backgroundMuted};
  font-size: ${props => props.theme.fontSizes.sm};
  transition: ${props => props.theme.transitions.default};

  &:focus {
    outline: none;
    box-shadow: ${props => props.theme.shadows.focus};
    border-color: ${props => props.theme.colors.primary};
    background-color: ${props => props.theme.colors.white};
  }

  &::placeholder {
    color: ${props => props.theme.colors.textMuted};
  }
`;

const CloseSearchButton = styled.button`
  position: absolute;
  right: ${props => props.theme.space[2]};
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: ${props => props.theme.colors.textMuted};

  & > svg {
    width: 14px;
    height: 14px;
  }

  &:hover {
    color: ${props => props.theme.colors.text};
  }
`;

export default Topbar;
