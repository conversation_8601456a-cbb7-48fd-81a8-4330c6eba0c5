// src/components/LegalBetConfirmationModalSimple.tsx
import React from 'react';
import styled from 'styled-components';
import * as Dialog from '@radix-ui/react-dialog';
import { XMarkIcon } from '@heroicons/react/24/outline';
import type { LegalBetRegistrationData } from '../types/legalBets';
import { DECLARATION_TYPES } from '../types/legalBets';
import { MONTHS } from '../types/misc';

interface LegalBetConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  data: LegalBetRegistrationData;
  isLoading?: boolean;
}

const LegalBetConfirmationModal: React.FC<LegalBetConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  data,
  isLoading = false
}) => {
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('es-VE', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const getMonthName = (monthNumber: number) => {
    const month = MONTHS.find(m => m.value === monthNumber);
    return month ? month.label : monthNumber.toString();
  };

  const getDeclarationTypeName = (type: string) => {
    const declarationType = DECLARATION_TYPES.find(dt => dt.value === type);
    return declarationType ? declarationType.label : type;
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Overlay />
        <Content>
          <Header>
            <Title>Confirmar Declaración de Apuestas Lícitas</Title>
            <CloseButton onClick={onClose}>
              <XMarkIcon />
            </CloseButton>
          </Header>

          <Body>
            <Description>
              Por favor, revise los datos antes de confirmar la declaración:
            </Description>

            <DataSection>
              <SectionTitle>Datos del Contribuyente</SectionTitle>
              <DataGrid>
                <DataItem>
                  <DataLabel>RIF:</DataLabel>
                  <DataValue>{data.rif}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Nombre o Razón Social:</DataLabel>
                  <DataValue>{data.nameOrBusinessName}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>N° de Cuenta:</DataLabel>
                  <DataValue>{data.accountNumber}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Teléfono:</DataLabel>
                  <DataValue>{data.phone}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Email:</DataLabel>
                  <DataValue>{data.email}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Dirección:</DataLabel>
                  <DataValue>{data.address}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Estado:</DataLabel>
                  <DataValue>{data.state}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Municipio:</DataLabel>
                  <DataValue>{data.municipality}</DataValue>
                </DataItem>
              </DataGrid>
            </DataSection>

            <DataSection>
              <SectionTitle>Periodo de la Declaración</SectionTitle>
              <DataGrid>
                <DataItem>
                  <DataLabel>Esquema de pago:</DataLabel>
                  <DataValue>{getDeclarationTypeName(data.declarationType)}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Periodo:</DataLabel>
                  <DataValue>{getMonthName(data.month)} {data.year}</DataValue>
                </DataItem>
              </DataGrid>
            </DataSection>

            <DataSection>
              <SectionTitle>Apuestas Registradas</SectionTitle>
              {data.betEntries.length > 0 ? (
                <>
                  <BetTable>
                    <TableHeader>
                      <TableRow>
                        <TableHeaderCell>Tipo de Apuesta</TableHeaderCell>
                        <TableHeaderCell>N° Apuestas</TableHeaderCell>
                        <TableHeaderCell>Monto Apostado</TableHeaderCell>
                        <TableHeaderCell>Alícuota</TableHeaderCell>
                        <TableHeaderCell>Impuesto</TableHeaderCell>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data.betEntries.map((entry, index) => (
                        <TableRow key={index}>
                          <TableCell>{entry.betTypeName}</TableCell>
                          <TableCell>{entry.numberOfBets}</TableCell>
                          <TableCell>{formatCurrency(entry.betAmount)}</TableCell>
                          <TableCell>{entry.taxRate}</TableCell>
                          <TableCell>{formatCurrency(entry.taxAmount)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </BetTable>
                  <TotalSection>
                    <TotalLabel>Total a pagar:</TotalLabel>
                    <TotalAmount>{formatCurrency(data.totalAmount)}</TotalAmount>
                  </TotalSection>
                </>
              ) : (
                <DataItem>
                  <DataValue>No se han registrado apuestas</DataValue>
                </DataItem>
              )}
            </DataSection>
          </Body>

          <Footer>
            <CancelButton onClick={onClose} disabled={isLoading}>
              Cancelar
            </CancelButton>
            <ConfirmButton onClick={onConfirm} disabled={isLoading}>
              {isLoading ? 'Procesando...' : 'Confirmar Declaración'}
            </ConfirmButton>
          </Footer>
        </Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

// Styled Components
const Overlay = styled(Dialog.Overlay)`
  background-color: rgba(0, 0, 0, 0.7);
  position: fixed;
  inset: 0;
  z-index: 1000;
`;

const Content = styled(Dialog.Content)`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  box-shadow: ${props => props.theme.shadows.xl};
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 800px;
  max-height: 85vh;
  overflow-y: auto;
  z-index: 1001;
  transition: background-color 0.3s ease;
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.space[6]};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const Title = styled(Dialog.Title)`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
  transition: color 0.3s ease;
`;

const CloseButton = styled.button`
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: ${props => props.theme.colors.textLight};
  cursor: pointer;
  border-radius: ${props => props.theme.radii.md};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.text};
  }

  svg {
    width: 20px;
    height: 20px;
  }
`;

const Body = styled.div`
  padding: ${props => props.theme.space[6]};
`;

const Description = styled.p`
  margin: 0 0 ${props => props.theme.space[6]};
  color: ${props => props.theme.colors.textLight};
  font-size: ${props => props.theme.fontSizes.md};
  transition: color 0.3s ease;
`;

const DataSection = styled.div`
  margin-bottom: ${props => props.theme.space[6]};

  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h3`
  margin: 0 0 ${props => props.theme.space[4]};
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const DataGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.space[3]};

  @media (min-width: 640px) {
    grid-template-columns: 1fr 1fr;
  }
`;

const DataItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[1]};
`;

const DataLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const DataValue = styled.span`
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.text};
  word-break: break-word;
  transition: color 0.3s ease;
`;

// Table styles
const BetTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: ${props => props.theme.space[4]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  overflow: hidden;
`;

const TableHeader = styled.thead`
  background-color: ${props => props.theme.colors.backgroundMuted};
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  &:not(:last-child) {
    border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  }
`;

const TableHeaderCell = styled.th`
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[1]};
  text-align: left;
  font-weight: ${props => props.theme.fontWeights.semibold};
  font-size: ${props => props.theme.fontSizes.xs};
  color: ${props => props.theme.colors.text};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const TableCell = styled.td`
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[1]};
  font-size: ${props => props.theme.fontSizes.xs};
  color: ${props => props.theme.colors.text};
  vertical-align: middle;
`;

const TotalSection = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: ${props => props.theme.space[3]};
  padding: ${props => props.theme.space[3]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  border: 1px solid ${props => props.theme.colors.borderLight};
`;

const TotalLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
`;

const TotalAmount = styled.span`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.primary};
  min-width: 100px;
  text-align: right;
`;

const Footer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${props => props.theme.space[3]};
  padding: ${props => props.theme.space[6]};
  border-top: 1px solid ${props => props.theme.colors.borderLight};
`;

const Button = styled.button`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[6]};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const CancelButton = styled(Button)`
  background-color: transparent;
  color: ${props => props.theme.colors.textLight};
  border-color: ${props => props.theme.colors.borderLight};

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.text};
  }
`;

const ConfirmButton = styled(Button)`
  background-color: ${props => props.theme.colors.primary};
  color: white;

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.primaryHover};
  }
`;

export default LegalBetConfirmationModal;
