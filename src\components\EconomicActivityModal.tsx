// src/components/EconomicActivityModal.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import * as Dialog from '@radix-ui/react-dialog';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';
import type { EconomicActivity } from '../types/economicActivity';
import economicActivitiesData from '../data/economicActivities.json';

interface EconomicActivityModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (activity: EconomicActivity) => void;
  selectedActivity?: EconomicActivity | null;
}

const EconomicActivityModal: React.FC<EconomicActivityModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  selectedActivity
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredActivities, setFilteredActivities] = useState<EconomicActivity[]>(economicActivitiesData);

  // Filter activities based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredActivities(economicActivitiesData);
      return;
    }

    const filtered = economicActivitiesData.filter(activity =>
      activity.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.descri.toLowerCase().includes(searchTerm.toLowerCase())
    );

    setFilteredActivities(filtered);
  }, [searchTerm]);

  const handleSelect = (activity: EconomicActivity) => {
    onSelect(activity);
    onClose();
  };

  const handleClose = () => {
    setSearchTerm('');
    onClose();
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleClose}>
      <Dialog.Portal>
        <DialogOverlay />
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Seleccionar Actividad Económica</DialogTitle>
            <DialogClose onClick={handleClose}>
              <XMarkIcon />
            </DialogClose>
          </DialogHeader>

          <SearchContainer>
            <SearchInputContainer>
              <SearchIcon>
                <MagnifyingGlassIcon />
              </SearchIcon>
              <SearchInput
                type="text"
                placeholder="Buscar por número, código o descripción..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </SearchInputContainer>
          </SearchContainer>

          <TableContainer>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHeaderCell>Número</TableHeaderCell>
                  <TableHeaderCell>Código</TableHeaderCell>
                  <TableHeaderCell>Descripción</TableHeaderCell>
                  <TableHeaderCell>Alícuota</TableHeaderCell>
                  <TableHeaderCell>MMV</TableHeaderCell>
                  <TableHeaderCell>Acción</TableHeaderCell>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredActivities.length > 0 ? (
                  filteredActivities.map((activity) => (
                    <TableRow 
                      key={activity.number}
                      $selected={selectedActivity?.number === activity.number}
                    >
                      <TableCell>{activity.number}</TableCell>
                      <TableCell>{activity.code}</TableCell>
                      <TableCell>{activity.descri}</TableCell>
                      <TableCell>{activity.alicuota}</TableCell>
                      <TableCell>{activity.mmv}</TableCell>
                      <TableCell>
                        <SelectButton
                          onClick={() => handleSelect(activity)}
                          $selected={selectedActivity?.number === activity.number}
                        >
                          {selectedActivity?.number === activity.number ? 'Seleccionada' : 'Seleccionar'}
                        </SelectButton>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6}>
                      <NoResults>
                        No se encontraron actividades económicas que coincidan con la búsqueda.
                      </NoResults>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <DialogFooter>
            <CancelButton onClick={handleClose}>
              Cancelar
            </CancelButton>
          </DialogFooter>
        </DialogContent>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

// Styled Components
const DialogOverlay = styled(Dialog.Overlay)`
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  inset: 0;
  z-index: 50;
`;

const DialogContent = styled(Dialog.Content)`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  box-shadow: ${props => props.theme.shadows.xl};
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 1200px;
  max-height: 85vh;
  padding: 0;
  z-index: 51;
  display: flex;
  flex-direction: column;
`;

const DialogHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.space[6]};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const DialogTitle = styled(Dialog.Title)`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const DialogClose = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: ${props => props.theme.radii.md};
  border: none;
  background-color: transparent;
  color: ${props => props.theme.colors.textLight};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  svg {
    width: 20px;
    height: 20px;
  }

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.text};
  }
`;

const SearchContainer = styled.div`
  padding: ${props => props.theme.space[4]} ${props => props.theme.space[6]};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const SearchInputContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const SearchIcon = styled.div`
  position: absolute;
  left: ${props => props.theme.space[3]};
  color: ${props => props.theme.colors.textLight};
  z-index: 1;

  svg {
    width: 20px;
    height: 20px;
  }
`;

const SearchInput = styled.input`
  width: 100%;
  height: 42px;
  padding: 0 ${props => props.theme.space[3]} 0 ${props => props.theme.space[10]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  background-color: ${props => props.theme.colors.white};
  color: ${props => props.theme.colors.text};
  transition: ${props => props.theme.transitions.default};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => `${props.theme.colors.primary}20`};
  }

  &::placeholder {
    color: ${props => props.theme.colors.textLight};
  }
`;

const TableContainer = styled.div`
  flex: 1;
  overflow: auto;
  padding: ${props => props.theme.space[4]} ${props => props.theme.space[6]};
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.thead`
  background-color: ${props => props.theme.colors.backgroundMuted};
  position: sticky;
  top: 0;
  z-index: 1;
`;

const TableRow = styled.tr<{ $selected?: boolean }>`
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  background-color: ${props => props.$selected ? `${props.theme.colors.primary}10` : 'transparent'};
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${props => props.$selected ? `${props.theme.colors.primary}15` : props.theme.colors.backgroundMuted};
  }
`;

const TableHeaderCell = styled.th`
  padding: ${props => props.theme.space[3]};
  text-align: left;
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  border-bottom: 2px solid ${props => props.theme.colors.borderLight};
`;

const TableCell = styled.td`
  padding: ${props => props.theme.space[3]};
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text};
  vertical-align: top;
`;

const TableBody = styled.tbody``;

const SelectButton = styled.button<{ $selected?: boolean }>`
  padding: ${props => props.theme.space[1]} ${props => props.theme.space[3]};
  border: 1px solid ${props => props.$selected ? props.theme.colors.success : props.theme.colors.primary};
  border-radius: ${props => props.theme.radii.md};
  background-color: ${props => props.$selected ? props.theme.colors.success : props.theme.colors.primary};
  color: white;
  font-size: ${props => props.theme.fontSizes.xs};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};
  min-width: 80px;

  &:hover {
    background-color: ${props => props.$selected ? props.theme.colors.successHover : props.theme.colors.primaryHover};
    border-color: ${props => props.$selected ? props.theme.colors.successHover : props.theme.colors.primaryHover};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const NoResults = styled.div`
  text-align: center;
  padding: ${props => props.theme.space[8]};
  color: ${props => props.theme.colors.textLight};
  font-style: italic;
`;

const DialogFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${props => props.theme.space[3]};
  padding: ${props => props.theme.space[6]};
  border-top: 1px solid ${props => props.theme.colors.borderLight};
`;

const CancelButton = styled.button`
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[4]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  background-color: transparent;
  color: ${props => props.theme.colors.text};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: ${props => props.theme.transitions.default};

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted};
  }
`;

export default EconomicActivityModal;
