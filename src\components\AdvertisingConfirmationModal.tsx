// src/components/AdvertisingConfirmationModal.tsx
import React from 'react';
import styled from 'styled-components';
import * as Dialog from '@radix-ui/react-dialog';
import { XMarkIcon } from '@heroicons/react/24/outline';
import type { AdvertisingRegistrationData } from '../types/advertising';

interface AdvertisingConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  data: AdvertisingRegistrationData;
  isLoading?: boolean;
}

const AdvertisingConfirmationModal: React.FC<AdvertisingConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  data,
  isLoading = false
}) => {
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('es-VE', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Overlay />
        <Content>
          <Header>
            <Title>Confirmar Declaración de Publicidad y Propaganda</Title>
            <CloseButton onClick={onClose}>
              <XMarkIcon />
            </CloseButton>
          </Header>

          <Body>
            <Description>
              Por favor, revise los datos antes de confirmar la declaración:
            </Description>

            <DataSection>
              <SectionTitle>Datos del Contribuyente</SectionTitle>
              <DataGrid>
                <DataItem>
                  <DataLabel>RIF:</DataLabel>
                  <DataValue>{data.rif}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Nombre o Razón Social:</DataLabel>
                  <DataValue>{data.nameOrBusinessName}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>N° de Cuenta:</DataLabel>
                  <DataValue>{data.accountNumber}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Teléfono:</DataLabel>
                  <DataValue>{data.phone}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Email:</DataLabel>
                  <DataValue>{data.email}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Dirección:</DataLabel>
                  <DataValue>{data.address}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Estado:</DataLabel>
                  <DataValue>{data.state}</DataValue>
                </DataItem>
                <DataItem>
                  <DataLabel>Municipio:</DataLabel>
                  <DataValue>{data.municipality}</DataValue>
                </DataItem>
              </DataGrid>
            </DataSection>

            <DataSection>
              <SectionTitle>Publicidad Registrada</SectionTitle>
              {data.advertisingEntries.length > 0 ? (
                <>
                  <AdvertisingTable>
                    <TableHeader>
                      <TableRow>
                        <TableHeaderCell>Tipo</TableHeaderCell>
                        <TableHeaderCell>Descripción</TableHeaderCell>
                        <TableHeaderCell>Dirección exacta</TableHeaderCell>
                        <TableHeaderCell>Mts2</TableHeaderCell>
                        <TableHeaderCell>N° Elementos</TableHeaderCell>
                        <TableHeaderCell>N° Días</TableHeaderCell>
                        <TableHeaderCell>Fracción</TableHeaderCell>
                        <TableHeaderCell>Tipo Cambio</TableHeaderCell>
                        <TableHeaderCell>Mes</TableHeaderCell>
                        <TableHeaderCell>Año</TableHeaderCell>
                        <TableHeaderCell>Impuesto</TableHeaderCell>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data.advertisingEntries.map((entry, index) => (
                        <TableRow key={index}>
                          <TableCell>{entry.type}</TableCell>
                          <TableCell>{entry.description}</TableCell>
                          <TableCell>{entry.exactAddress}</TableCell>
                          <TableCell>{entry.area}</TableCell>
                          <TableCell>{entry.numberOfElements}</TableCell>
                          <TableCell>{entry.numberOfDays}</TableCell>
                          <TableCell>{entry.fraction}</TableCell>
                          <TableCell>{entry.exchangeType}</TableCell>
                          <TableCell>{data.month}</TableCell>
                          <TableCell>{data.year}</TableCell>
                          <TableCell>{formatCurrency(entry.taxAmount)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </AdvertisingTable>
                  <TotalSection>
                    <TotalLabel>Total a pagar:</TotalLabel>
                    <TotalAmount>{formatCurrency(data.totalAmount)}</TotalAmount>
                  </TotalSection>
                </>
              ) : (
                <DataItem>
                  <DataValue>No se ha registrado publicidad</DataValue>
                </DataItem>
              )}
            </DataSection>
          </Body>

          <Footer>
            <CancelButton onClick={onClose} disabled={isLoading}>
              Cancelar
            </CancelButton>
            <ConfirmButton onClick={onConfirm} disabled={isLoading}>
              {isLoading ? 'Procesando...' : 'Confirmar Declaración'}
            </ConfirmButton>
          </Footer>
        </Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

// Styled Components
const Overlay = styled(Dialog.Overlay)`
  background-color: rgba(0, 0, 0, 0.7);
  position: fixed;
  inset: 0;
  z-index: 1000;
`;

const Content = styled(Dialog.Content)`
  background-color: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.radii.lg};
  box-shadow: ${props => props.theme.shadows.xl};
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 900px;
  max-height: 85vh;
  overflow-y: auto;
  z-index: 1001;
  transition: background-color 0.3s ease;
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.space[6]};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
`;

const Title = styled(Dialog.Title)`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  margin: 0;
  transition: color 0.3s ease;
`;

const CloseButton = styled.button`
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: ${props => props.theme.colors.textLight};
  cursor: pointer;
  border-radius: ${props => props.theme.radii.md};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.backgroundMuted};
    color: ${props => props.theme.colors.text};
  }

  svg {
    width: 20px;
    height: 20px;
  }
`;

const Body = styled.div`
  padding: ${props => props.theme.space[6]};
`;

const Description = styled.p`
  margin: 0 0 ${props => props.theme.space[6]};
  color: ${props => props.theme.colors.textLight};
  font-size: ${props => props.theme.fontSizes.md};
  transition: color 0.3s ease;
`;

const DataSection = styled.div`
  margin-bottom: ${props => props.theme.space[6]};

  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h3`
  margin: 0 0 ${props => props.theme.space[4]};
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const DataGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${props => props.theme.space[3]};

  @media (min-width: 640px) {
    grid-template-columns: 1fr 1fr;
  }
`;

const DataItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[1]};
`;

const DataLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const DataValue = styled.span`
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.text};
  word-break: break-word;
  transition: color 0.3s ease;
`;

const AdvertisingTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: ${props => props.theme.space[4]};
  font-size: ${props => props.theme.fontSizes.sm};
`;

const TableHeader = styled.thead`
  background-color: ${props => props.theme.colors.backgroundMuted};
  transition: background-color 0.3s ease;
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  transition: border-color 0.3s ease;
`;

const TableHeaderCell = styled.th`
  padding: ${props => props.theme.space[3]};
  text-align: left;
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  transition: all 0.3s ease;
`;

const TableCell = styled.td`
  padding: ${props => props.theme.space[3]};
  color: ${props => props.theme.colors.text};
  border-bottom: 1px solid ${props => props.theme.colors.borderLight};
  transition: all 0.3s ease;
`;

const TotalSection = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: ${props => props.theme.space[4]};
  padding: ${props => props.theme.space[4]};
  background-color: ${props => props.theme.colors.backgroundMuted};
  border-radius: ${props => props.theme.radii.md};
  margin-top: ${props => props.theme.space[4]};
  transition: background-color 0.3s ease;
`;

const TotalLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const TotalAmount = styled.span`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.primary};
  transition: color 0.3s ease;
`;

const Footer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${props => props.theme.space[3]};
  padding: ${props => props.theme.space[6]};
  border-top: 1px solid ${props => props.theme.colors.borderLight};
  transition: border-color 0.3s ease;
`;

const CancelButton = styled.button`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[6]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  background-color: ${props => props.theme.colors.white};
  color: ${props => props.theme.colors.text};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.backgroundMuted};
    border-color: ${props => props.theme.colors.border};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ConfirmButton = styled.button`
  padding: ${props => props.theme.space[3]} ${props => props.theme.space[6]};
  border: none;
  background-color: ${props => props.theme.colors.primary};
  color: white;
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background-color: ${props => props.theme.colors.primaryDark};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

export default AdvertisingConfirmationModal;
