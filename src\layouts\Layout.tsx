// src/layouts/Layout.tsx
import React, { useEffect } from 'react';
import styled from 'styled-components';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import Sidebar from '../components/Sidebar';
import Topbar from '../components/Topbar';
import { useResponsiveSidebar } from '../hooks/useResponsiveSidebar';

const Layout: React.FC = () => {
  const {
    isSidebarCollapsed,
    isMobile,
    isOverlayVisible,
    toggleSidebar,
    closeSidebar
  } = useResponsiveSidebar();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the current page title based on the route
  const getPageTitle = () => {
    const path = location.pathname;

    if (path === '/') return 'Inicio';

    // Registros y submenús
    if (path === '/registros') return 'Registros';
    if (path === '/registros/actividades-economicas') return 'Registros - Actividades Económicas';
    if (path === '/registros/inmuebles-urbanos') return 'Registros - Inmuebles Urbanos';
    if (path === '/registros/vehiculos') return 'Registro de Vehículos';
    if (path === '/registros/publicidad-propaganda') return 'Registros - Publicidad y Propaganda';
    if (path === '/registros/espectaculos-publicos') return 'Registros - Espectáculos públicos';
    if (path === '/registros/apuestas-licitas') return 'Registros - Apuestas lícitas';
    if (path === '/registros/tasas') return 'Registros - Tasas';

    // Procesos tributarios y submenús
    if (path === '/procesos-tributarios') return 'Procesos Tributarios';
    if (path === '/procesos-tributarios/rebajas-servicios') return 'Procesos Tributarios - Rebajas de Servicios';

    // Agentes de retenciones y submenús
    if (path === '/agentes-retenciones') return 'Agentes de Retenciones';
    if (path === '/agentes-retenciones/retenciones-iae') return 'Agentes de Retenciones - Retenciones IAE';

    // Consultas y submenús
    if (path === '/consultas') return 'Consultas';
    if (path === '/consultas/estado-cuenta') return 'Consultas - Estado de Cuenta';

    // Pagos
    if (path === '/pagos') return 'Pagos';

    // Base legal y submenús
    if (path === '/base-legal') return 'Base Legal';
    if (path === '/base-legal/actividades-economicas') return 'Base Legal - Actividades Económicas';
    if (path === '/base-legal/inmuebles-urbanos') return 'Base Legal - Inmuebles Urbanos';
    if (path === '/base-legal/aseo-urbano') return 'Base Legal - Aseo Urbano';
    if (path === '/base-legal/gas-domestico') return 'Base Legal - Gas Doméstico';
    if (path === '/base-legal/vehiculos') return 'Base Legal - Vehículos';
    if (path === '/base-legal/licores') return 'Base Legal - Licores';
    if (path === '/base-legal/publicidad-propaganda') return 'Base Legal - Publicidad y Propaganda';
    if (path === '/base-legal/espectaculos-publicos') return 'Base Legal - Espectáculos públicos';
    if (path === '/base-legal/apuestas-licitas') return 'Base Legal - Apuestas Lícitas';
    if (path === '/base-legal/tasas-administrativas') return 'Base Legal - Tasas Administrativas';

    // Autogestión
    if (path === '/autogestion') return 'Autogestión de Servicios Municipales';

    return 'Taxia';
  };

  // Update document title when route changes
  useEffect(() => {
    const pageTitle = getPageTitle();
    document.title = pageTitle === 'Inicio' ? 'Taxia' : `Taxia | ${pageTitle}`;
  }, [location.pathname]);

  const handleLogout = () => {
    // Here you would handle the logout logic
    // For now, just navigate to login page
    navigate('/login');
  };

  return (
    <LayoutContainer>
      <Sidebar
        isCollapsed={isSidebarCollapsed}
        isMobile={isMobile}
        isOverlayVisible={isOverlayVisible}
        closeSidebar={closeSidebar}
      />

      <MainContainer $isCollapsed={isSidebarCollapsed} $isMobile={isMobile}>
        <Topbar
          title={getPageTitle()}
          isCollapsed={isSidebarCollapsed}
          toggleSidebar={toggleSidebar}
          onLogout={handleLogout}
        />

        <ContentContainer>
          <Outlet />
        </ContentContainer>
      </MainContainer>
    </LayoutContainer>
  );
};

// Styled Components
const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
`;

const MainContainer = styled.div<{ $isCollapsed: boolean; $isMobile: boolean }>`
  flex: 1;
  margin-left: ${props => props.$isMobile ? '0' : (props.$isCollapsed ? props.theme.sizes.sidebarCollapsedWidth : props.theme.sizes.sidebarWidth)};
  transition: margin-left ${props => props.theme.transitions.default};

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    margin-left: 0;
  }
`;

const ContentContainer = styled.main`
  padding: ${props => props.theme.space[6]};
  margin-top: ${props => props.theme.sizes.topbarHeight};
  min-height: calc(100vh - ${props => props.theme.sizes.topbarHeight});
  background-color: ${props => props.theme.colors.background};
  transition: background-color 0.3s ease;
`;

export default Layout;
