// src/components/ToastProvider.tsx
import React, { createContext, useContext, useState, ReactNode } from 'react';
import * as Toast from '@radix-ui/react-toast';
import { Cross2Icon } from '@radix-ui/react-icons';
import { 
  ToastViewport, 
  ToastRoot, 
  ToastTitle, 
  ToastDescription, 
  ToastClose 
} from '../styles/ToastStyles';

type ToastVariant = 'info' | 'success' | 'warning' | 'error';

interface ToastContextType {
  showToast: (title: string, description?: string, variant?: ToastVariant) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [variant, setVariant] = useState<ToastVariant>('info');
  
  const showToast = (
    title: string, 
    description: string = '', 
    variant: ToastVariant = 'info'
  ) => {
    setTitle(title);
    setDescription(description);
    setVariant(variant);
    setOpen(true);
  };
  
  return (
    <ToastContext.Provider value={{ showToast }}>
      <Toast.Provider swipeDirection="right">
        {children}
        
        <ToastRoot 
          open={open} 
          onOpenChange={setOpen} 
          variant={variant}
        >
          <ToastTitle>{title}</ToastTitle>
          {description && <ToastDescription>{description}</ToastDescription>}
          <ToastClose aria-label="Close">
            <Cross2Icon />
          </ToastClose>
        </ToastRoot>
        
        <ToastViewport />
      </Toast.Provider>
    </ToastContext.Provider>
  );
};

export default ToastProvider;
