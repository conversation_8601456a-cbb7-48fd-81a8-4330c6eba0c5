// src/components/ThemeToggle.tsx
import React from 'react';
import styled from 'styled-components';
import { MoonIcon, SunIcon } from '@radix-ui/react-icons';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle: React.FC = () => {
  const { themeMode, toggleTheme } = useTheme();

  return (
    <ToggleButton
      onClick={toggleTheme}
      aria-label={themeMode === 'light' ? 'Switch to dark theme' : 'Switch to light theme'}
      title={themeMode === 'light' ? 'Switch to dark theme' : 'Switch to light theme'}
    >
      {themeMode === 'light' ? <MoonIcon /> : <SunIcon />}
    </ToggleButton>
  );
};

const ToggleButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: ${props => props.theme.radii.md};
  color: ${props => props.theme.colors.textLight};
  transition: ${props => props.theme.transitions.default};

  &:hover {
    background-color: ${props =>
      props.theme.themeMode === 'light'
        ? 'rgba(0, 0, 0, 0.05)'
        : 'rgba(255, 255, 255, 0.05)'
    };
    color: ${props => props.theme.colors.text};
  }

  & > svg {
    width: 16px;
    height: 16px;
  }
`;

export default ThemeToggle;
