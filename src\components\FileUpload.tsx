// src/components/FileUpload.tsx
import React, { useState, useRef, useEffect } from 'react';
import type { DragEvent, ChangeEvent } from 'react';
import styled from 'styled-components';
import { DocumentIcon, XMarkIcon, CloudArrowUpIcon } from '@heroicons/react/24/outline';
import { VehicleApiService } from '../services/vehicleApi';

interface FileUploadProps {
  onFileSelect: (file: File | null) => void;
  selectedFile: File | null;
  error?: string;
  accept?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFileSelect,
  selectedFile,
  error,
  accept = '.pdf,.jpg,.jpeg,.png'
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Reset file input and errors when selectedFile becomes null (form reset)
  useEffect(() => {
    if (selectedFile === null) {
      setUploadError('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, [selectedFile]);

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileSelection = (file: File) => {
    setUploadError('');

    // Validate file
    const validation = VehicleApiService.validateFile(file);
    if (!validation.isValid) {
      setUploadError(validation.error || 'Archivo no válido');
      return;
    }

    onFileSelect(file);
  };

  const handleRemoveFile = () => {
    onFileSelect(null);
    setUploadError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const displayError = error || uploadError;

  return (
    <Container>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileInputChange}
        style={{ display: 'none' }}
      />

      {!selectedFile ? (
        <DropZone
          $isDragOver={isDragOver}
          $hasError={!!displayError}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <UploadIcon>
            <CloudArrowUpIcon />
          </UploadIcon>
          <UploadText>
            <strong>Haga clic para subir</strong> o arrastre y suelte
          </UploadText>
          <UploadSubtext>
            PDF, JPG, PNG hasta 5MB
          </UploadSubtext>
        </DropZone>
      ) : (
        <FilePreview>
          <FileInfo>
            <FileIcon>
              <DocumentIcon />
            </FileIcon>
            <FileDetails>
              <FileName>{selectedFile.name}</FileName>
              <FileSize>{formatFileSize(selectedFile.size)}</FileSize>
            </FileDetails>
          </FileInfo>
          <RemoveButton onClick={handleRemoveFile}>
            <XMarkIcon />
          </RemoveButton>
        </FilePreview>
      )}

      {displayError && (
        <ErrorMessage>{displayError}</ErrorMessage>
      )}
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  width: 100%;
`;

const DropZone = styled.div<{ $isDragOver: boolean; $hasError: boolean }>`
  border: 2px dashed ${props =>
    props.$hasError ? props.theme.colors.danger :
    props.$isDragOver ? props.theme.colors.primary :
    props.theme.colors.borderLight
  };
  border-radius: ${props => props.theme.radii.lg};
  padding: ${props => props.theme.space[8]};
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: ${props =>
    props.$isDragOver ? `${props.theme.colors.primary}10` :
    props.theme.colors.backgroundMuted
  };

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    background-color: ${props => `${props.theme.colors.primary}05`};
  }
`;

const UploadIcon = styled.div`
  width: 48px;
  height: 48px;
  margin: 0 auto ${props => props.theme.space[4]};
  color: ${props => props.theme.colors.textLight};

  svg {
    width: 100%;
    height: 100%;
  }
`;

const UploadText = styled.p`
  margin: 0 0 ${props => props.theme.space[2]};
  font-size: ${props => props.theme.fontSizes.md};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const UploadSubtext = styled.p`
  margin: 0;
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const FilePreview = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.space[4]};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-radius: ${props => props.theme.radii.md};
  background-color: ${props => props.theme.colors.backgroundMuted};
  transition: all 0.3s ease;
`;

const FileInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.space[3]};
`;

const FileIcon = styled.div`
  width: 40px;
  height: 40px;
  color: ${props => props.theme.colors.primary};

  svg {
    width: 100%;
    height: 100%;
  }
`;

const FileDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.space[1]};
`;

const FileName = styled.span`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text};
  transition: color 0.3s ease;
`;

const FileSize = styled.span`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.textLight};
  transition: color 0.3s ease;
`;

const RemoveButton = styled.button`
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: ${props => props.theme.colors.textLight};
  cursor: pointer;
  border-radius: ${props => props.theme.radii.md};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.theme.colors.danger}20;
    color: ${props => props.theme.colors.danger};
  }

  svg {
    width: 20px;
    height: 20px;
  }
`;

const ErrorMessage = styled.div`
  margin-top: ${props => props.theme.space[2]};
  padding: ${props => props.theme.space[2]} ${props => props.theme.space[3]};
  background-color: ${props => `${props.theme.colors.danger}20`};
  color: ${props => props.theme.colors.danger};
  border-radius: ${props => props.theme.radii.md};
  font-size: ${props => props.theme.fontSizes.sm};
  transition: all 0.3s ease;
`;

export default FileUpload;
